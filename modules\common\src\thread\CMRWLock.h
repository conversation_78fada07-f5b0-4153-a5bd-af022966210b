#ifndef _CM_RWLOCK_H_
#define _CM_RWLOCK_H_

#include "CM_pubhead.h"

#ifdef WIN32
//#include <process.h>
#include <windows.h>
#else
#include <pthread.h> 
#endif

class CMRWLock
{
public:
    CMRWLock();
    virtual ~CMRWLock();

public:
    cx_int Initialize();
    cx_int Destroy();
    cx_int LockR();
    cx_int UnlockR();
    cx_int LockW();
    cx_int UnlockW();

private:

#ifdef WIN32
    SRWLOCK     m_srwLock;
#else
    pthread_rwlock_t m_rwlock;
#endif // WIN32

};

template<typename T>
class CMSharedData : public CMRWLock
{
public:
    T   m_data;
};

#endif // _CM_RWLOCK_H_
