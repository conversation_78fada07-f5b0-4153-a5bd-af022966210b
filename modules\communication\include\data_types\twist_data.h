/**
 * @file twist_data.h    
 * @brief This file contains the definition of the TwistData class, which stores linear and angular velocity data.
 */
#pragma once

namespace communication {

// Twist数据类，存储线速度和角速度数据
class TwistData {
    public:
        double time_ = 0.0; // Twist数据的时间戳，单位为秒
        
        // 线速度 (m/s)
        struct {
            double x = 0.0;
            double y = 0.0; 
            double z = 0.0;
        } linear;
        
        // 角速度 (rad/s)
        struct {
            double x = 0.0;
            double y = 0.0;
            double z = 0.0;
        } angular;

        TwistData() = default;
        
        TwistData(double vx, double vy, double vz, double wx, double wy, double wz) {
            linear.x = vx;
            linear.y = vy;
            linear.z = vz;
            angular.x = wx;
            angular.y = wy;
            angular.z = wz;
        }
        
        void setZero() {
            linear.x = linear.y = linear.z = 0.0;
            angular.x = angular.y = angular.z = 0.0;
        }
        
        bool isZero() const {
            return (fabs(linear.x) < 1e-6 && fabs(linear.y) < 1e-6 && fabs(linear.z) < 1e-6 &&
                    fabs(angular.x) < 1e-6 && fabs(angular.y) < 1e-6 && fabs(angular.z) < 1e-6);
        }
};

}  // namespace communication
