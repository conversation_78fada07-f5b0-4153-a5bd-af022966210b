#ifndef _CM_OBSERVER_H_
#define _CM_OBSERVER_H_

#include "CM_pubhead.h"

#include <iostream>
#include <set>
#include <string>
using namespace std;

class CMObservable;

//�۲��ߣ��������
class CMObserver
{
public:
    CMObserver() {};
    virtual ~CMObserver() {};

    //�����۲��Ŀ�귢���仯ʱ��֪ͨ���ø÷���
    //���Ա��۲���pObs, ��չ����ΪpArg
    virtual void Update(CMObservable* pObs, void* pArg = NULL) = 0;
};

enum SUBJECT_TYPE
{
    ST_NONE,
    ST_VECHICLE_POSITION,
    ST_MM_RESULT,
    ST_RC_RESTULT,
    ST_MPP_RESULT,//MPP result
    ST_SENSOR_UBLOX
};

//���۲��ߣ���Subject
class CMObservable
{
public:
    CMObservable() : m_bChanged(false),m_eSubjectType(ST_NONE) {};
    virtual ~CMObservable() {};

    void Attach(CMObserver* pObs);      //ע��۲���
    void Detach(CMObserver* pObs);     //ע���۲���
    void DetachAll();                               //ע�����й۲���

    void Notify(void* pArg = NULL);       //��״̬�仯��������۲��ߣ����֪ͨ����

    bool HasChanged();                          //����Ŀ��״̬�Ƿ�仯
    int GetObserversCount();            //��ȡ�۲�������

    SUBJECT_TYPE GetType();

protected:
    void SetChanged();                          //����״̬�仯!!!����̳�CObservable��������Ŀ��״̬
    void ClearChanged();                       //��ʼ��Ŀ��Ϊδ�仯״̬

private:
    bool m_bChanged;                          //״̬
    set<CMObserver*> m_setObs;        //set��֤Ŀ��Ψһ��

protected:
    SUBJECT_TYPE    m_eSubjectType;
};


#endif // _CM_OBSERVER_H_
