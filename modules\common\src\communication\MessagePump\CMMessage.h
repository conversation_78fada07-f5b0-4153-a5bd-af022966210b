#ifndef CMMESSAGE_H
#define CMMESSAGE_H

#include "CM_pubhead.h"

enum MSG_APP_ID
{
    MSG_APP_UNKNOW = 0,
    MSG_APP_CAN,
    MSG_APP_ZMQ,
    MSG_APP_SERIAL,
    MSG_APP_ROS,
};

enum MSG_ID
{
    MSG_ID_UNKNOW = 0,
    MSG_ID_GPS ,
    MSG_ID_IMU ,
    MSG_ID_LIDAR_OUST64 ,
    MSG_ID_LIDAR_VELO16 ,
    MSG_ID_LIDAR_RS32 ,
    MSG_ID_MANUAL_POS ,
    MSG_ID_CAMERA ,
    MSG_ID_SYNC_DATA,
    MSG_ID_TRANS_POINT_CLOUD,
    MSG_ID_RELOC_POINT_CLOUD,
    MSG_ID_GLOAB_MAP_CLOUD,
    MSG_ID_INIT_CLOUD,
    MSG_ID_SURROUD_CLOUD,
    MSG_ID_FINAL_CLOUD,
    MSG_ID_DOWN_SIZE_GLOAB_MAP,
    MSG_ID_LC,
};

class CMMessage
{
public:
    CMMessage();

public:
    cx_uint   appId;
    cx_uint   messageId;
    cx_uint   wParam;
    void*     lParam;
    cx_uint64 time;

public:
    CMMessage& operator=(const CMMessage& rightValue);
};

typedef std::deque<CMMessage*> CMMessageDeque;

#endif // CMMESSAGE_H
