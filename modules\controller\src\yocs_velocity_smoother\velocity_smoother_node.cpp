#include "velocity_smoother.h"
#include <iostream>
#include <chrono>
#include <thread>
#include <signal.h>

using namespace yocs_velocity_smoother;

// 全局变量用于控制程序运行
static bool g_running = true;

// 信号处理函数
void signalHandler(int signum) {
    std::cout << "\n🛑 接收到终止信号 " << signum << std::endl;
    g_running = false;
}

/**
 * @brief 主程序
 */
int main(int argc, char** argv) {
    std::cout << "=== VelocitySmootherNoRos 基础示例程序 ===" << std::endl;
    
    // 注册信号处理函数
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    try {
        // 创建速度平滑器
        VelocitySmootherNoRos smoother("velocity_smoother_demo");

        // 设置基本参数
        std::cout << "\n🔧 配置速度平滑器参数..." << std::endl;
        smoother.setSpeedLimits(2.0, 1.5);      // 速度限制
        smoother.setAccelLimits(1.0, 0.8);      // 加速度限制
        smoother.setDecelFactor(2.0);           // 减速因子
        smoother.setFrequency(50.0);            // 控制频率
        smoother.setQuiet(false);               // 启用调试输出
        smoother.setRobotFeedback(0);           // 无机器人反馈

        // 设置输出回调函数
        std::cout << "\n📡 设置回调函数..." << std::endl;
        smoother.setSmoothVelPublishCallback([](const std::shared_ptr<TwistMsg>& vel) {
            std::cout << "输出速度: linear=" << vel->linear.x
                      << " m/s, angular=" << vel->angular.z << " rad/s" << std::endl;
        });

        // 初始化
        std::cout << "\n⚙️ 初始化速度平滑器..." << std::endl;
        if (!smoother.init()) {
            std::cerr << "❌ 速度平滑器初始化失败" << std::endl;
            return -1;
        }

        // 启动速度平滑器
        std::cout << "\n🚀 启动速度平滑器..." << std::endl;
        smoother.start();

        // 等待启动完成
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        std::cout << "\n✅ 速度平滑器已启动并运行" << std::endl;
        std::cout << "按 Ctrl+C 停止程序" << std::endl;

        // 主循环
        while (g_running) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        // 停止速度平滑器
        std::cout << "\n🛑 正在停止速度平滑器..." << std::endl;
        smoother.stop();
        
        std::cout << "\n✅ 程序正常退出" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序执行出错: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
