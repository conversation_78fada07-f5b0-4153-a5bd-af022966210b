/**
 *  @file publisher_base.h
 *  @brief This file contains the base class for publishing data.
 */
#pragma once

#include <deque>
#include <mutex>
#include "data_struct.h"

namespace communication{

// using LidarDataCallback = std::function<void(const CloudData&)>;
// template<typename T, typename T_MSG>
template<typename T>
class PublisherBase {
    public:
        PublisherBase(const std::string &topic, size_t max_buffer_size) : topic_(topic),
            // data_(data),
            max_buffer_size_(max_buffer_size) {
            // Constructor initializes the topic and maximum buffer size
            // Additional initialization can be done here if needed

        }
        virtual ~PublisherBase() = default;

        void Publish(const T& data) {
            data_ = data; // Update the data to be published
            if (GetSubscriberCount() > 0) {
                // Convert data to message type and publish
                ToMsg();
                PublishMsg();
            } 
            else {
                // ROS_WARN("No subscribers for topic: %s", topic_.c_str());
            }
        }
        
        std::string GetTopic() const {
            return topic_;
        }

    protected:
        virtual void PublishMsg(){
            
        }
        virtual void ToMsg(){

        }
         // This function should return the number of subscribers to the topic
        virtual int GetSubscriberCount() const {
            return 0;   
        }; 

        T data_;           // Data to be published
    //    T_MSG msg_;        // Message to be published
        std::string topic_;// Topic name for publishing data
        size_t max_buffer_size_ = 20; // Maximum buffer size for published data

        // LidarDataCallback lidar_data_callback_ = nullptr; // Callback function for IMU data
    };

using IntDataPublisherBase = PublisherBase<int>;
using DoubleDataPublisherBase = PublisherBase<double>;    
using StringDataPublisherBase = PublisherBase<std::string>;
using BoolDataPublisherBase = PublisherBase<bool>;
using FloatDataPublisherBase = PublisherBase<float>;

// using PointCloudDataPublisherBase = PublisherBase<CloudData>;
using OdometryPublisherBase = PublisherBase<PoseVelData>;
using CloudDataPublisherBase = PublisherBase<CloudData>;
using PathDataPublisherBase = PublisherBase<PathData>;
using PoseDataPublisherBase = PublisherBase<PoseData>;

} // namespace communication