#pragma once
#include <pcl/point_types.h>
#include <pcl/point_cloud.h>


struct EIGEN_ALIGN16 PointXYZRI
{
  PCL_ADD_POINT4D;
  //float intensity;
  PCL_ADD_INTENSITY;
  float time;
  uint16_t ring;
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
};

POINT_CLOUD_REGISTER_POINT_STRUCT(PointXYZRI,
                                  (float, x, x)(float, y, y)(float, z, z)(float, intensity, intensity)(float, time, time)(std::uint16_t, ring, ring))

namespace communication {

struct CloudData {
    // using PointCloud = pcl::PointCloud<pcl::PointXYZI>;
    // using PointCloudPtr = PointCloud::Ptr;

    using POINT = pcl::PointXYZI;
    using CLOUD = pcl::PointCloud<POINT>;
    using CLOUD_PTR = CLOUD::Ptr;
    // using PointCloudPtr = CLOUD::Ptr;

    CloudData()
      :cloud_ptr(new CLOUD()) {
    }

    double time = 0.0;
    CLOUD_PTR cloud_ptr;
};

struct CloudNormalData {
    using PointXYZINormalType = pcl::PointXYZINormal;
    using CLOUD_NormalType = pcl::PointCloud<PointXYZINormalType>;
    using CLOUD_NormalType_PTR = CLOUD_NormalType::Ptr;

    CloudNormalData()
      :m_cloudPtr(new CLOUD_NormalType()) {
    }

    double m_time = 0.0; // time unit： s 
    CLOUD_NormalType_PTR m_cloudPtr;
};

struct CloudXYZRIData {
  // using PointCloud = pcl::PointCloud<pcl::PointXYZI>;
  // using PointCloudPtr = PointCloud::Ptr;

  // using POINT = pcl::PointXYZI;
  using CLOUD = pcl::PointCloud<PointXYZRI>;
  using CLOUD_PTR = CLOUD::Ptr;
  // using PointCloudPtr = CLOUD::Ptr;

  CloudXYZRIData()
    :cloud_ptr(new CLOUD()) {
  }

  double time = 0.0;
  CLOUD_PTR cloud_ptr;
};

} // namespace communication {
