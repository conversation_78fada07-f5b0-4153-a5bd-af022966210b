﻿#ifndef _ME_CONST_H_
#define _ME_CONST_H_

#include "CM_types.h"

#define IN
#define OUT
#define INOUT

#define MAX_PATH          260

constexpr hash_t prime = 0x100000001B3ull;
constexpr hash_t basis = 0xCBF29CE484222325ull;

#ifdef MEM_CHECK
    void CM_FreeMemory(void *p);
#else
    #define CM_FreeMemory(...)
#endif

#define DELETE_S(pObject) if(NULL != pObject) {CM_FreeMemory(pObject); delete (pObject); (pObject) = NULL;}
#define DELETE_SG(pObject) if(NULL != pObject) {CM_FreeMemory(pObject); delete[] (pObject); (pObject) = NULL;}

#endif // !_ME_CONST_H_
