#ifndef LOCAL_PLANNER_H
#define LOCAL_PLANNER_H

#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <mutex>
#include <chrono>
#include <iostream>
#include <fstream>
#include <thread>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/common/time.h>
#include <pcl/registration/icp.h>
#include <pcl/io/pcd_io.h>
#include <Eigen/Dense>
#include <yaml-cpp/yaml.h>

namespace local_planner {

const double PI = 3.1415926;

/**
 * @brief 时间戳结构
 */
struct TimeStamp {
    double sec;
    
    TimeStamp() : sec(0.0) {}
    TimeStamp(double s) : sec(s) {}
    
    static TimeStamp now() {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        double seconds = std::chrono::duration<double>(duration).count();
        return TimeStamp(seconds);
    }
    
    double toSec() const { return sec; }
};

/**
 * @brief 消息头结构
 */
struct Header {
    TimeStamp stamp;
    std::string frame_id;
    
    Header() : frame_id("") {}
    
    void setCurrentTime() {
        stamp = TimeStamp::now();
    }
};

/**
 * @brief 3D点结构
 */
struct Point {
    double x, y, z;
    
    Point() : x(0.0), y(0.0), z(0.0) {}
    Point(double _x, double _y, double _z) : x(_x), y(_y), z(_z) {}
};

/**
 * @brief 四元数结构
 */
struct Quaternion {
    double x, y, z, w;
    
    Quaternion() : x(0.0), y(0.0), z(0.0), w(1.0) {}
    Quaternion(double _x, double _y, double _z, double _w) : x(_x), y(_y), z(_z), w(_w) {}
    
    // 从欧拉角创建四元数
    static Quaternion fromYaw(double yaw) {
        Quaternion q;
        q.w = cos(yaw * 0.5);
        q.x = 0.0;
        q.y = 0.0;
        q.z = sin(yaw * 0.5);
        return q;
    }
    
    // 转换为欧拉角
    double toYaw() const {
        return atan2(2.0 * (w * z + x * y), 1.0 - 2.0 * (y * y + z * z));
    }
};

/**
 * @brief 位姿结构
 */
struct Pose {
    Point position;
    Quaternion orientation;
    
    Pose() {}
    Pose(double x, double y, double z, double yaw) 
        : position(x, y, z), orientation(Quaternion::fromYaw(yaw)) {}
};

/**
 * @brief 带时间戳的位姿
 */
struct PoseStamped {
    Header header;
    Pose pose;
    
    PoseStamped() {}
    PoseStamped(double x, double y, double z, double yaw) : pose(x, y, z, yaw) {
        header.setCurrentTime();
    }
};

/**
 * @brief 里程计数据结构
 */
struct OdometryData {
    Header header;
    Pose pose;
    
    OdometryData() {}
    OdometryData(double x, double y, double z, double roll, double pitch, double yaw) 
        : pose(x, y, z, yaw) {
        header.setCurrentTime();
    }
};

/**
 * @brief 路径点结构
 */
struct PathPoint {
    Point position;
    Quaternion orientation;
    
    PathPoint() {}
    PathPoint(double x, double y, double z) : position(x, y, z) {}
};

/**
 * @brief 路径数据结构
 */
struct PathData {
    Header header;
    std::vector<PathPoint> poses;
    
    PathData() {}
    
    void resize(size_t size) {
        poses.resize(size);
    }
    
    size_t size() const {
        return poses.size();
    }
};

/**
 * @brief 点云数据结构
 */
struct PointCloud2Data {
    Header header;
    std::vector<uint8_t> data;
    uint32_t height;
    uint32_t width;
    
    PointCloud2Data() : height(0), width(0) {}
};

/**
 * @brief 布尔消息
 */
struct BoolMsg {
    bool data;
    
    BoolMsg() : data(false) {}
    BoolMsg(bool value) : data(value) {}
};

/**
 * @brief 整数消息
 */
struct Int8Msg {
    int8_t data;
    
    Int8Msg() : data(0) {}
    Int8Msg(int8_t value) : data(value) {}
};

/**
 * @brief 多边形数据结构
 */
struct PolygonStamped {
    Header header;
    std::vector<Point> points;
    
    PolygonStamped() {}
};

/**
 * @brief 导航结果数据结构
 */
struct NavigationResult {
    Header header;
    int8_t result_code;
    std::string message;
    
    NavigationResult() : result_code(0) {}
};

/**
 * @brief 导航目标数据结构
 */
struct NavigationTarget {
    Header header;
    PoseStamped target_pose;
    int8_t nav_mode;
    int32_t point_id;
    int8_t point_info;
    int8_t speed;
    int8_t manner;
    int8_t obsmode;
    
    NavigationTarget() : nav_mode(0), point_id(0), point_info(0), speed(0), manner(0), obsmode(0) {}
};

/**
 * @brief 局部规划器配置参数结构体
 */
struct LocalPlannerConfig {
    // 路径文件夹
    std::string pathFolder;
    
    // 车辆参数
    double vehicleLength;
    double vehicleWidth;
    double sensorOffsetX;
    double sensorOffsetY;
    bool twoWayDrive;
    
    // 点云处理参数
    double laserVoxelSize;
    double terrainVoxelSize;
    bool useTerrainAnalysis;
    bool checkRotObstacle;
    double adjacentRange;
    double obstacleHeightThre;
    double groundHeightThre;
    double costHeightThre;
    double costScore;
    bool useCost;
    int pointPerPathThre;
    double minRelZ;
    double maxRelZ;
    
    // 路径规划参数
    double dirWeight;
    double dirThre;
    bool dirToVehicle;
    double pathScale;
    double minPathScale;
    double pathScaleStep;
    bool pathScaleBySpeed;
    double minPathRange;
    double pathRangeStep;
    bool pathRangeBySpeed;
    bool pathCropByGoal;
    double goalClearRange;
    double arrived_dis_threshold;
    
    // 速度参数
    double maxSpeed;
    
    // 网格参数
    float gridVoxelSize;
    float searchRadius;
    float gridVoxelOffsetX;
    float gridVoxelOffsetY;
    
    // 默认构造函数
    LocalPlannerConfig() {
        // 设置默认值
        pathFolder = "/home/<USER>/NR_Navigation/src/local_planner";
        vehicleLength = 1.2;
        vehicleWidth = 0.8;
        sensorOffsetX = 0.0;
        sensorOffsetY = 0.0;
        twoWayDrive = false;
        
        laserVoxelSize = 0.05;
        terrainVoxelSize = 0.2;
        useTerrainAnalysis = true;
        checkRotObstacle = false;
        adjacentRange = 4.25;
        obstacleHeightThre = 0.15;
        groundHeightThre = 0.1;
        costHeightThre = 0.1;
        costScore = 0.02;
        useCost = false;
        pointPerPathThre = 2;
        minRelZ = -0.8;
        maxRelZ = 0.25;
        
        dirWeight = 0.02;
        dirThre = 90.0;
        dirToVehicle = false;
        pathScale = 1.25;
        minPathScale = 0.75;
        pathScaleStep = 0.25;
        pathScaleBySpeed = true;
        minPathRange = 1.0;
        pathRangeStep = 0.5;
        pathRangeBySpeed = true;
        pathCropByGoal = true;
        goalClearRange = 0.5;
        arrived_dis_threshold = 0.2;
        
        maxSpeed = 0.8;
        
        gridVoxelSize = 0.02;
        searchRadius = 0.45;
        gridVoxelOffsetX = 3.2;
        gridVoxelOffsetY = 4.5;
    }
};

// 回调函数类型定义
using OdometryCallback = std::function<void(const std::shared_ptr<const OdometryData>&)>;
using PathCallback = std::function<void(const std::shared_ptr<const PathData>&)>;
using BoolCallback = std::function<void(const std::shared_ptr<const BoolMsg>&)>;
using Int8Callback = std::function<void(const std::shared_ptr<const Int8Msg>&)>;
using NavigationResultCallback = std::function<void(const std::shared_ptr<const NavigationResult>&)>;

/**
 * @brief 去ROS化的局部规划器类
 */
class LocalPlannerNoRos {
public:
    // 构造函数和析构函数
    LocalPlannerNoRos(const std::string& config_path = "");
    ~LocalPlannerNoRos();

    // 初始化函数
    bool initialize();
    bool loadConfig(const std::string& config_path);

    // 数据输入接口
    void updateOdometry(const OdometryData& odom);
    void updateLaserCloud(const PointCloud2Data& cloud);
    void updateTerrainCloud(const PointCloud2Data& cloud);
    void updateGoal(const PoseStamped& goal);
    void updateTarget(const PoseStamped& target);
    void updateWebTarget(const PoseStamped& target);
    void updateBoundary(const PolygonStamped& boundary);
    void updateAddedObstacles(const PointCloud2Data& obstacles);
    void updateLocationFailed(const BoolMsg& msg);
    void updateCalibration(const Int8Msg& calibration);

    // 输出回调设置
    void setModePublishCallback(BoolCallback callback);
    void setPathPublishCallback(PathCallback callback);
    void setStopPublishCallback(Int8Callback callback);
    void setInnerStopPublishCallback(Int8Callback callback);
    void setReplanPublishCallback(Int8Callback callback);
    void setNodeReadyPublishCallback(BoolCallback callback);
    void setFreePathsPublishCallback(std::function<void(const std::shared_ptr<PointCloud2Data>&)> callback);

    // 控制接口
    void startPlanning();
    void stopPlanning();
    void pausePlanning();
    void resumePlanning();

    // 参数设置
    void setMaxSpeed(double speed);
    void setVehicleDimensions(double length, double width);
    void setPathScale(double scale);
    void setAdjacentRange(double range);
    void setUseTerrainAnalysis(bool use);

    // 状态查询
    bool isInitialized() const;
    bool isRunning() const;
    bool isPaused() const;
    LocalPlannerConfig getConfig() const;
    void printStatus();

    // 主循环方法
    void controlLoop();
    void processOnce();

private:
    // 配置参数
    LocalPlannerConfig config_;
    std::string config_path_;

    // 运行状态
    bool initialized_;
    bool running_;
    bool paused_;
    std::mutex data_mutex_;

    // 回调函数
    BoolCallback mode_publish_callback_;
    PathCallback path_publish_callback_;
    Int8Callback stop_publish_callback_;
    Int8Callback inner_stop_publish_callback_;
    Int8Callback replan_publish_callback_;
    BoolCallback node_ready_publish_callback_;
    std::function<void(const std::shared_ptr<PointCloud2Data>&)> free_paths_publish_callback_;

    // 原始变量保留 (与原localPlanner.cpp中的全局变量对应)
    BoolMsg arrive_inf;
    BoolMsg adjustmode;
    int nav_start;
    int info;
    int id;
    double goalX, goalY, goalZ;
    double targetX, targetY, targetZ, targetYaw;
    float joyDir;
    bool newLaserCloud;
    bool newTerrainCloud;
    double odomTime;
    float vehicleRoll, vehiclePitch, vehicleYaw;
    float vehicleX, vehicleY, vehicleZ;
    bool init;
    double start_time, end_time;
    bool location_failed;

    // 路径相关常量和变量
    static const int pathNum = 343;
    static const int groupNum = 7;
    static const int gridVoxelNumX = 161;
    static const int gridVoxelNumY = 451;
    static const int gridVoxelNum = gridVoxelNumX * gridVoxelNumY;
    static const int laserCloudStackNum = 1;

    int laserCloudCount;
    int pathList[pathNum];
    Eigen::Vector3f endDirPosPathList[pathNum];
    int clearPathList[36 * pathNum];
    float pathPenaltyList[36 * pathNum];
    float clearPathPerGroupScore[36 * groupNum];
    std::vector<int> correspondences[gridVoxelNum];

    // PCL点云
    pcl::PointCloud<pcl::PointXYZI>::Ptr cloudKeyPoses3D;
    pcl::PointCloud<pcl::PointXYZI>::Ptr pointview;
    pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloud;
    pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloudCrop;
    pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloudDwz;
    pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloud;
    pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloudCrop;
    pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloudDwz;
    pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloudStack[laserCloudStackNum];
    pcl::PointCloud<pcl::PointXYZI>::Ptr plannerCloud;
    pcl::PointCloud<pcl::PointXYZI>::Ptr plannerCloudCrop;
    pcl::PointCloud<pcl::PointXYZI>::Ptr boundaryCloud;
    pcl::PointCloud<pcl::PointXYZI>::Ptr addedObstacles;
    pcl::PointCloud<pcl::PointXYZ>::Ptr startPaths[groupNum];
    pcl::PointCloud<pcl::PointXYZI>::Ptr paths[pathNum];
    pcl::PointCloud<pcl::PointXYZI>::Ptr freePaths;

    // 滤波器
    pcl::VoxelGrid<pcl::PointXYZI> laserDwzFilter, terrainDwzFilter;

    // 私有方法 (保留原始函数功能)
    void odometryHandler(const std::shared_ptr<const OdometryData>& odom);
    void laserCloudHandler(const std::shared_ptr<const PointCloud2Data>& laserCloud2);
    void terrainCloudHandler(const std::shared_ptr<const PointCloud2Data>& terrainCloud2);
    void goalHandler(const std::shared_ptr<const PoseStamped>& goal);
    void targetHandler(const std::shared_ptr<const PoseStamped>& target);
    void webtargetHandler(const std::shared_ptr<const PoseStamped>& target);
    void boundaryHandler(const std::shared_ptr<const PolygonStamped>& boundary);
    void addedObstaclesHandler(const std::shared_ptr<const PointCloud2Data>& addedObstacles2);
    void locationFailedHandler(const std::shared_ptr<const BoolMsg>& msg);
    void calibrationHandler(const std::shared_ptr<const Int8Msg>& calibration);

    // 文件读取方法
    int readPlyHeader(FILE *filePtr);
    void readStartPaths();
    void readPaths();
    void readPathList();
    void readCorrespondences();

    // 工具方法
    double getCurrentTime();
    void initializePointClouds();
    void initializeFilters();
    bool loadYamlConfig(const std::string& config_path);

    // PCL点云转换方法
    void convertPointCloud2ToPCL(const PointCloud2Data& cloud2, pcl::PointCloud<pcl::PointXYZI>& pcl_cloud);
    void convertPCLToPointCloud2(const pcl::PointCloud<pcl::PointXYZI>& pcl_cloud, PointCloud2Data& cloud2);

    // 路径规划核心算法
    void performPathPlanning(float pathRange, float relativeGoalDis);
};

} // namespace local_planner

#endif // LOCAL_PLANNER_H
