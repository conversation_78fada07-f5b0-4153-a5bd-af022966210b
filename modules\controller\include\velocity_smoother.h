#ifndef VELOCITY_SMOOTHER_NO_ROS_H
#define VELOCITY_SMOOTHER_NO_ROS_H

/*****************************************************************************
 ** Includes
 *****************************************************************************/

#include <string>
#include <vector>
#include <mutex>
#include <thread>
#include <chrono>
#include <functional>
#include <memory>
#include <iostream>
#include <algorithm>
#include <cmath>
#include <atomic>

// YAML解析库 (可选)
#ifdef USE_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

/*****************************************************************************
 ** Preprocessing (保留原有宏定义)
 *****************************************************************************/

#define PERIOD_RECORD_SIZE    5
#define ZERO_VEL_COMMAND      TwistMsg()
#define IS_ZERO_VEOCITY(a)   ((a.linear.x == 0.0) && (a.angular.z == 0.0))

/*****************************************************************************
** 去ROS化的数据结构定义
*****************************************************************************/

/**
 * @brief 去ROS化的Vector3数据结构 (替代geometry_msgs::Vector3)
 */
struct Vector3 {
    double x, y, z;
    
    Vector3() : x(0.0), y(0.0), z(0.0) {}
    Vector3(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
    
    Vector3& operator=(const Vector3& other) {
        x = other.x;
        y = other.y;
        z = other.z;
        return *this;
    }
    
    bool operator==(const Vector3& other) const {
        return (x == other.x) && (y == other.y) && (z == other.z);
    }
    
    bool operator!=(const Vector3& other) const {
        return !(*this == other);
    }
};

/**
 * @brief 去ROS化的Twist数据结构 (替代geometry_msgs::Twist)
 */
struct TwistMsg {
    Vector3 linear;
    Vector3 angular;
    
    TwistMsg() {}
    TwistMsg(double linear_x, double angular_z) {
        linear.x = linear_x;
        angular.z = angular_z;
    }
    
    TwistMsg& operator=(const TwistMsg& other) {
        linear = other.linear;
        angular = other.angular;
        return *this;
    }
    
    bool operator==(const TwistMsg& other) const {
        return (linear == other.linear) && (angular == other.angular);
    }
    
    bool operator!=(const TwistMsg& other) const {
        return !(*this == other);
    }
};

/**
 * @brief 去ROS化的Odometry数据结构 (替代nav_msgs::Odometry)
 */
struct OdometryMsg {
    struct Header {
        double stamp;
        std::string frame_id;
        Header() : stamp(0.0), frame_id("odom") {}
    } header;
    
    struct PoseWithCovariance {
        struct Pose {
            Vector3 position;
            struct Quaternion {
                double x, y, z, w;
                Quaternion() : x(0), y(0), z(0), w(1) {}
            } orientation;
        } pose;
        std::vector<double> covariance; // 6x6 covariance matrix
    } pose;
    
    struct TwistWithCovariance {
        TwistMsg twist;
        std::vector<double> covariance; // 6x6 covariance matrix
    } twist;
    
    OdometryMsg() {
        pose.covariance.resize(36, 0.0);
        twist.covariance.resize(36, 0.0);
    }
};

/**
 * @brief 去ROS化的时间结构 (替代ros::Time)
 */
class TimeStamp {
public:
    TimeStamp() : time_point_(std::chrono::steady_clock::now()) {}
    
    static TimeStamp now() {
        return TimeStamp();
    }
    
    double toSec() const {
        auto duration = time_point_.time_since_epoch();
        return std::chrono::duration<double>(duration).count();
    }
    
    TimeStamp operator-(const TimeStamp& other) const {
        TimeStamp result;
        result.time_point_ = time_point_ - (other.time_point_ - std::chrono::steady_clock::time_point{});
        return result;
    }
    
private:
    std::chrono::steady_clock::time_point time_point_;
};

/**
 * @brief 去ROS化的发布器模板 (替代ros::Publisher)
 */
template<typename T>
class Publisher {
public:
    using CallbackType = std::function<void(const std::shared_ptr<T>&)>;
    
    Publisher() = default;
    
    void setCallback(CallbackType callback) {
        callback_ = callback;
    }
    
    void publish(const std::shared_ptr<T>& message) {
        if (callback_) {
            callback_(message);
        }
    }
    
    void publish(const T& message) {
        auto msg_ptr = std::make_shared<T>(message);
        publish(msg_ptr);
    }
    
private:
    CallbackType callback_;
};

/**
 * @brief 去ROS化的订阅器模板 (替代ros::Subscriber)
 */
template<typename T>
class Subscriber {
public:
    using CallbackType = std::function<void(const std::shared_ptr<const T>&)>;
    
    Subscriber() = default;
    
    void setCallback(CallbackType callback) {
        callback_ = callback;
    }
    
    void processMessage(const std::shared_ptr<const T>& message) {
        if (callback_) {
            callback_(message);
        }
    }
    
    void processMessage(const T& message) {
        auto msg_ptr = std::make_shared<const T>(message);
        processMessage(msg_ptr);
    }
    
private:
    CallbackType callback_;
};

/**
 * @brief 去ROS化的参数配置结构 (替代dynamic_reconfigure)
 */
struct ParamsConfig {
    double speed_lim_v;
    double speed_lim_w;
    double accel_lim_v;
    double accel_lim_w;
    double decel_factor;
    
    ParamsConfig() 
        : speed_lim_v(1.0), speed_lim_w(1.0), accel_lim_v(1.0), 
          accel_lim_w(1.0), decel_factor(1.0) {}
};

/**
 * @brief 去ROS化的动态重配置服务器 (替代dynamic_reconfigure::Server)
 */
class DynamicReconfigureServer {
public:
    using CallbackType = std::function<void(ParamsConfig&, uint32_t)>;
    
    DynamicReconfigureServer() = default;
    
    void setCallback(CallbackType callback) {
        callback_ = callback;
    }
    
    void updateConfig(const ParamsConfig& config) {
        ParamsConfig mutable_config = config;
        if (callback_) {
            callback_(mutable_config, 0);
        }
    }
    
private:
    CallbackType callback_;
};

/*****************************************************************************
** Namespaces
*****************************************************************************/

namespace yocs_velocity_smoother {

/*****************************************************************************
** VelocitySmoother (保留所有原有功能、函数和变量)
*****************************************************************************/

class VelocitySmootherNoRos
{
public:
    VelocitySmootherNoRos(const std::string &name);

    ~VelocitySmootherNoRos()
    {
        if (dynamic_reconfigure_server != nullptr)
            delete dynamic_reconfigure_server;
    }

    bool init();
    bool initFromConfig(const std::string& config_file = "");
    void spin();
    void shutdown() { shutdown_req = true; }
    
    // 保留原有的互斥锁
    std::mutex locker;

    // 新增的去ROS化接口
    void setVelocityCallback(std::function<void(const std::shared_ptr<TwistMsg>&)> callback);
    void setOdometryCallback(std::function<void(const std::shared_ptr<OdometryMsg>&)> callback);
    void setRobotVelCallback(std::function<void(const std::shared_ptr<TwistMsg>&)> callback);
    void setSmoothVelPublishCallback(std::function<void(const std::shared_ptr<TwistMsg>&)> callback);
    
    // 数据输入接口
    void inputVelocity(const TwistMsg& msg);
    void inputOdometry(const OdometryMsg& msg);
    void inputRobotVel(const TwistMsg& msg);
    
    // 参数设置接口
    void setSpeedLimits(double speed_lim_v, double speed_lim_w);
    void setAccelLimits(double accel_lim_v, double accel_lim_w);
    void setDecelFactor(double decel_factor);
    void setFrequency(double frequency);
    void setQuiet(bool quiet);
    void setRobotFeedback(int feedback_type);
    
    // 参数获取接口
    double getSpeedLimV() const { return speed_lim_v; }
    double getSpeedLimW() const { return speed_lim_w; }
    double getAccelLimV() const { return accel_lim_v; }
    double getAccelLimW() const { return accel_lim_w; }
    double getDecelFactor() const { return decel_factor; }
    double getFrequency() const { return frequency; }
    bool getQuiet() const { return quiet; }
    int getRobotFeedback() const { return static_cast<int>(robot_feedback); }
    
    // 状态查询接口
    bool isInitialized() const { return initialized_; }
    bool isInputActive() const { return input_active; }
    TwistMsg getLastCmdVel() const { return last_cmd_vel; }
    TwistMsg getCurrentVel() const { return current_vel; }
    TwistMsg getTargetVel() const { return target_vel; }
    
    // 运行控制
    void start();
    void stop();
    bool isRunning() const { return running_; }

private:
    // 保留原有的枚举类型
    enum RobotFeedbackType
    {
        NONE,
        ODOMETRY,
        COMMANDS
    } robot_feedback;  /**< What source to use as robot velocity feedback */

    // 保留所有原有变量
    std::string name;
    bool quiet;        /**< Quieten some warnings that are unavoidable because of velocity multiplexing. **/
    double speed_lim_v, accel_lim_v, decel_lim_v;
    double speed_lim_w, accel_lim_w, decel_lim_w;
    double decel_factor;

    double frequency;

    TwistMsg last_cmd_vel;
    TwistMsg current_vel;
    TwistMsg target_vel;

    bool shutdown_req; /**< Shutdown requested by nodelet; kill worker thread */
    bool input_active;
    double cb_avg_time;
    TimeStamp last_cb_time;
    std::vector<double> period_record; /**< Historic of latest periods between velocity commands */
    unsigned int pr_next; /**< Next position to fill in the periods record buffer */

    // 去ROS化的发布器和订阅器
    Subscriber<TwistMsg> odometry_sub;    /**< Current velocity from odometry */
    Subscriber<TwistMsg> current_vel_sub; /**< Current velocity from commands sent to the robot */
    Subscriber<TwistMsg> raw_in_vel_sub;  /**< Incoming raw velocity commands */
    Publisher<TwistMsg> smooth_vel_pub;   /**< Outgoing smoothed velocity commands */

    // 保留所有原有回调函数
    void velocityCB(const std::shared_ptr<const TwistMsg>& msg);
    void robotVelCB(const std::shared_ptr<const TwistMsg>& msg);
    void odometryCB(const std::shared_ptr<const OdometryMsg>& msg);

    // 保留原有的工具函数
    double sign(double x) { return x < 0.0 ? -1.0 : +1.0; }

    double median(std::vector<double> values) {
        // Return the median element of an doubles vector
        nth_element(values.begin(), values.begin() + values.size()/2, values.end());
        return values[values.size()/2];
    }

    /*********************
    ** Dynamic Reconfigure (保留原有功能)
    **********************/
    DynamicReconfigureServer* dynamic_reconfigure_server;
    std::function<void(ParamsConfig&, uint32_t)> dynamic_reconfigure_callback;
    void reconfigCB(ParamsConfig &config, uint32_t unused_level);

    // 新增的内部状态变量
    bool initialized_;
    bool running_;
    std::thread worker_thread_;
    std::atomic<bool> thread_running_;
    
    // 回调函数
    std::function<void(const std::shared_ptr<TwistMsg>&)> velocity_callback_;
    std::function<void(const std::shared_ptr<OdometryMsg>&)> odometry_callback_;
    std::function<void(const std::shared_ptr<TwistMsg>&)> robot_vel_callback_;
    std::function<void(const std::shared_ptr<TwistMsg>&)> smooth_vel_publish_callback_;
    
    // 配置加载函数
    bool loadConfiguration(const std::string& config_file);
    bool loadConfigurationFromYAML(const std::string& yaml_file);
    bool loadConfigurationFromText(const std::string& config_file);
    void setDefaultConfiguration();
    void applyConfiguration();
    
    // 内部工具函数
    void printStatus() const;
    void validateParameters();
};

} // namespace yocs_velocity_smoother

#endif /* VELOCITY_SMOOTHER_NO_ROS_H */
