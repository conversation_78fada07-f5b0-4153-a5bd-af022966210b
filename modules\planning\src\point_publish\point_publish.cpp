#include "point_publish.h"
#include <iostream>
#include <iomanip>

#ifdef USE_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

using namespace std;
using namespace point_publish_no_ros;

/**
 * @brief 构造函数
 */
PointPublishNoRos::PointPublishNoRos(const std::string& name) 
    : name_(name), initialized_(false), running_(false) {
    setDefaultParameters();
}

/**
 * @brief 析构函数
 */
PointPublishNoRos::~PointPublishNoRos() {
    stop();
}

/**
 * @brief 初始化
 */
bool PointPublishNoRos::init() {
    if (initialized_) {
        std::cout << "PointPublishNoRos already initialized" << std::endl;
        return true;
    }
    
    initialized_ = true;
    std::cout << "PointPublishNoRos initialized successfully" << std::endl;
    return true;
}

/**
 * @brief 从配置文件初始化
 */
bool PointPublishNoRos::initFromConfig(const std::string& config_file) {
    if (!config_file.empty()) {
        if (!loadConfiguration(config_file)) {
            std::cerr << "Failed to load configuration from: " << config_file << std::endl;
            return false;
        }
    }
    return init();
}

/**
 * @brief 启动
 */
void PointPublishNoRos::start() {
    if (!initialized_) {
        std::cerr << "PointPublishNoRos not initialized" << std::endl;
        return;
    }
    
    if (running_) {
        std::cout << "PointPublishNoRos already running" << std::endl;
        return;
    }
    
    running_ = true;
    worker_thread_ = std::thread(&PointPublishNoRos::controlLoop, this);
    std::cout << "PointPublishNoRos started" << std::endl;
}

/**
 * @brief 停止
 */
void PointPublishNoRos::stop() {
    if (running_) {
        running_ = false;
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        std::cout << "PointPublishNoRos stopped" << std::endl;
    }
}

/**
 * @brief 设置所有参数 (完全保留原有参数)
 */
void PointPublishNoRos::setParameters(int point_id, int point_info, int gait, int speed, 
                                     int manner, int obsmode, int navmode) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    point_id_ = point_id;
    point_info_ = point_info;
    gait_ = gait;
    speed_ = speed;
    manner_ = manner;
    obsmode_ = obsmode;
    navmode_ = navmode;
}

/**
 * @brief 设置单个参数的函数
 */
void PointPublishNoRos::setPointId(int point_id) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    point_id_ = point_id;
}

void PointPublishNoRos::setPointInfo(int point_info) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    point_info_ = point_info;
}

void PointPublishNoRos::setGait(int gait) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    gait_ = gait;
}

void PointPublishNoRos::setSpeed(int speed) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    speed_ = speed;
}

void PointPublishNoRos::setManner(int manner) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    manner_ = manner;
}

void PointPublishNoRos::setObsmode(int obsmode) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    obsmode_ = obsmode;
}

void PointPublishNoRos::setNavmode(int navmode) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    navmode_ = navmode;
}

/**
 * @brief 输入目标点 (替换ROS订阅)
 */
void PointPublishNoRos::inputGoal(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    goalHandler(std::make_shared<const PoseStamped>(goal));
}

/**
 * @brief 输入Web目标点 (替换ROS订阅)
 */
void PointPublishNoRos::inputWebGoal(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    webgoalHandler(std::make_shared<const PoseStamped>(goal));
}

/**
 * @brief 设置导航目标回调函数 (替换ROS发布)
 */
void PointPublishNoRos::setNavigationTargetCallback(std::function<void(const std::shared_ptr<NavigationTarget>&)> callback) {
    nav_target_callback_ = callback;
}

/**
 * @brief 设置导航结果回调函数
 */
void PointPublishNoRos::setNavigationResultCallback(std::function<void(const std::shared_ptr<NavigationResult>&)> callback) {
    nav_result_callback_ = callback;
}

/**
 * @brief 状态查询函数
 */
bool PointPublishNoRos::isInitialized() const {
    return initialized_;
}

bool PointPublishNoRos::isRunning() const {
    return running_;
}

/**
 * @brief 获取当前参数
 */
int PointPublishNoRos::getPointId() const {
    return point_id_;
}

int PointPublishNoRos::getPointInfo() const {
    return point_info_;
}

int PointPublishNoRos::getGait() const {
    return gait_;
}

int PointPublishNoRos::getSpeed() const {
    return speed_;
}

int PointPublishNoRos::getManner() const {
    return manner_;
}

int PointPublishNoRos::getObsmode() const {
    return obsmode_;
}

int PointPublishNoRos::getNavmode() const {
    return navmode_;
}

/**
 * @brief 目标点处理函数 (完全保留原有实现)
 */
void PointPublishNoRos::goalHandler(const std::shared_ptr<const PoseStamped>& goal) {
    double roll, pitch, yaw;
    Quaternion geoQuat = goal->pose.orientation;
    
    // 完全保留原有的四元数到欧拉角转换逻辑
    geoQuat.getRPY(roll, pitch, yaw);
    
    // 创建导航目标消息 (完全保留原有逻辑)
    NavigationTarget target_point;
    target_point.pose_x = goal->pose.position.x;
    target_point.pose_y = goal->pose.position.y;
    target_point.pose_z = goal->pose.position.z;
    target_point.yaw = yaw;
    target_point.nav_mode = 1;
    target_point.point_id = point_id_;
    target_point.point_info = point_info_;
    target_point.gait = gait_;
    target_point.speed = speed_;
    target_point.manner = manner_;
    target_point.obsmode = obsmode_;
    target_point.navmode = navmode_;
    
    // 发布导航目标 (替换ROS发布)
    if (nav_target_callback_) {
        nav_target_callback_(std::make_shared<NavigationTarget>(target_point));
    }
    
    std::cout << "***********目标点已下发***********" << std::endl;
    std::cout << "位置: (" << target_point.pose_x << ", " << target_point.pose_y 
              << ", " << target_point.pose_z << ")" << std::endl;
    std::cout << "朝向: " << yaw * 57.3 << " 度" << std::endl;
    std::cout << "参数: point_id=" << target_point.point_id
              << ", gait=" << target_point.gait
              << ", speed=" << target_point.speed << std::endl;
}

/**
 * @brief 控制循环 (替换原有的ROS主循环)
 */
void PointPublishNoRos::controlLoop() {
    const double loop_rate = 100.0; // 100Hz，保持与原有频率一致
    const auto sleep_duration = std::chrono::duration<double>(1.0 / loop_rate);

    while (running_) {
        // 保持运行状态，等待输入
        std::this_thread::sleep_for(sleep_duration);
    }
}

/**
 * @brief 设置默认参数 (完全保留原有默认值)
 */
void PointPublishNoRos::setDefaultParameters() {
    point_id_ = 0;      // 原有默认值
    point_info_ = 0;    // 原有默认值
    gait_ = 0;          // 原有默认值
    speed_ = 1;         // 原有默认值
    manner_ = 0;        // 原有默认值
    obsmode_ = 1;       // 原有默认值
    navmode_ = 0;       // 原有默认值
}

/**
 * @brief 加载配置文件
 */
bool PointPublishNoRos::loadConfiguration(const std::string& config_file) {
    if (config_file.empty()) {
        std::cout << "No config file specified, using default parameters" << std::endl;
        return true;
    }

    // 根据文件扩展名选择加载方式
    if (config_file.find(".yaml") != std::string::npos ||
        config_file.find(".yml") != std::string::npos) {
        return loadConfigurationFromYAML(config_file);
    } else {
        return loadConfigurationFromText(config_file);
    }
}

/**
 * @brief 从YAML文件加载配置
 */
bool PointPublishNoRos::loadConfigurationFromYAML(const std::string& yaml_file) {
#ifdef USE_YAML_CPP
    try {
        YAML::Node config = YAML::LoadFile(yaml_file);

        if (config["point_id"]) point_id_ = config["point_id"].as<int>();
        if (config["point_info"]) point_info_ = config["point_info"].as<int>();
        if (config["gait"]) gait_ = config["gait"].as<int>();
        if (config["speed"]) speed_ = config["speed"].as<int>();
        if (config["manner"]) manner_ = config["manner"].as<int>();
        if (config["obsmode"]) obsmode_ = config["obsmode"].as<int>();
        if (config["navmode"]) navmode_ = config["navmode"].as<int>();

        std::cout << "Configuration loaded from YAML: " << yaml_file << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error loading YAML config: " << e.what() << std::endl;
        return false;
    }
#else
    std::cerr << "YAML support not compiled. Please install yaml-cpp and recompile." << std::endl;
    return false;
#endif
}

/**
 * @brief 从文本文件加载配置
 */
bool PointPublishNoRos::loadConfigurationFromText(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Cannot open config file: " << config_file << std::endl;
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        // 跳过注释和空行
        if (line.empty() || line[0] == '#') continue;

        size_t pos = line.find('=');
        if (pos != std::string::npos) {
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);

            // 去除空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            // 设置参数
            if (key == "point_id") point_id_ = std::stoi(value);
            else if (key == "point_info") point_info_ = std::stoi(value);
            else if (key == "gait") gait_ = std::stoi(value);
            else if (key == "speed") speed_ = std::stoi(value);
            else if (key == "manner") manner_ = std::stoi(value);
            else if (key == "obsmode") obsmode_ = std::stoi(value);
            else if (key == "navmode") navmode_ = std::stoi(value);
        }
    }

    file.close();
    std::cout << "Configuration loaded from text file: " << config_file << std::endl;
    return true;
}

/**
 * @brief 保存配置到文件
 */
bool PointPublishNoRos::saveConfiguration(const std::string& config_file) const {
    std::ofstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Cannot create config file: " << config_file << std::endl;
        return false;
    }

    file << "# PointPublish Configuration File" << std::endl;
    file << "# Generated automatically" << std::endl;
    file << std::endl;
    file << "point_id = " << point_id_ << std::endl;
    file << "point_info = " << point_info_ << std::endl;
    file << "gait = " << gait_ << std::endl;
    file << "speed = " << speed_ << std::endl;
    file << "manner = " << manner_ << std::endl;
    file << "obsmode = " << obsmode_ << std::endl;
    file << "navmode = " << navmode_ << std::endl;

    file.close();
    std::cout << "Configuration saved to: " << config_file << std::endl;
    return true;
}

/**
 * @brief 打印当前状态
 */
void PointPublishNoRos::printStatus() const {
    std::cout << "\n=== PointPublishNoRos Status ===" << std::endl;
    std::cout << "Name: " << name_ << std::endl;
    std::cout << "Initialized: " << (initialized_ ? "Yes" : "No") << std::endl;
    std::cout << "Running: " << (running_ ? "Yes" : "No") << std::endl;
    std::cout << std::endl;
    std::cout << "Parameters:" << std::endl;
    std::cout << "  point_id: " << point_id_ << std::endl;
    std::cout << "  point_info: " << point_info_ << std::endl;
    std::cout << "  gait: " << gait_ << std::endl;
    std::cout << "  speed: " << speed_ << std::endl;
    std::cout << "  manner: " << manner_ << std::endl;
    std::cout << "  obsmode: " << obsmode_ << std::endl;
    std::cout << "  navmode: " << navmode_ << std::endl;
    std::cout << "===============================" << std::endl;
}

/**
 * @brief Web目标点处理函数 (完全保留原有实现)
 */
void PointPublishNoRos::webgoalHandler(const std::shared_ptr<const PoseStamped>& goal) {
    double roll, pitch, yaw;
    Quaternion geoQuat = goal->pose.orientation;
    
    // 完全保留原有的四元数到欧拉角转换逻辑
    geoQuat.getRPY(roll, pitch, yaw);
    
    // 创建导航目标消息 (完全保留原有逻辑)
    NavigationTarget target_point;
    target_point.pose_x = goal->pose.position.x;
    target_point.pose_y = goal->pose.position.y;
    target_point.pose_z = goal->pose.position.z;
    target_point.yaw = yaw;
    target_point.nav_mode = 1;
    target_point.point_id = point_id_;
    target_point.point_info = point_info_;
    target_point.gait = gait_;
    target_point.speed = speed_;
    target_point.manner = manner_;
    target_point.obsmode = obsmode_;
    target_point.navmode = navmode_;
    
    // 发布导航目标 (替换ROS发布)
    if (nav_target_callback_) {
        nav_target_callback_(std::make_shared<NavigationTarget>(target_point));
    }
    
    std::cout << "***********web目标点已下发***********" << std::endl;
    std::cout << "位置: (" << target_point.pose_x << ", " << target_point.pose_y 
              << ", " << target_point.pose_z << ")" << std::endl;
    std::cout << "朝向: " << yaw * 57.3 << " 度" << std::endl;
    std::cout << "参数: point_id=" << target_point.point_id 
              << ", gait=" << target_point.gait 
              << ", speed=" << target_point.speed << std::endl;
}
