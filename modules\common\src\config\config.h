#ifndef _CONFIG_H_
#define _CONFIG_H_

#include <yaml-cpp/yaml.h>
#include <string>
#include <vector>
#include <memory>

class Config {
public:
    // 获取单例实例
    static Config& getInstance() {
        static Config instance;
        return instance;
    }

    // 初始化配置
    bool init(const std::string& config_file);

    // General parameters getter methods
    const std::string& getParentDir() const { return m_strParentDir; }
    const std::string& getMapDir() const { return m_strMapDir; }
    const std::string& getMapName() const { return m_strMapName; }
    const std::string& getPoseTxtName() const { return m_strPoseTxtName; }
    const std::string& getLoadMapPath() const { return m_strLoadMapPath; }
    const std::string& getLoadPosePath() const { return m_strLoadPosePath; }
    const std::string& getLog4cplusFilePath() const { return m_strLog4cplusFilePath; }
    const std::string& getLog4cplusOutfilePath() const { return m_strLog4cplusOutfilePath; }

    // Publishing flags getter methods
    bool isPathEnabled() const { return m_bPathEn; }
    bool isScanPubEnabled() const { return m_bScanPubEn; }
    bool isDensePubEnabled() const { return m_bDensePubEn; }
    bool isScanBodyPubEnabled() const { return m_bScanBodyPubEn; }
    bool isVisualIkdtreeEnabled() const { return m_bVisualIkdtree; }

    // Algorithm parameters getter methods
    int getMaxIterations() const { return m_nNumMaxIterations; }
    const std::string& getMapFilePath() const { return m_strMapFilePath; }

    // Topics getter methods
    const std::string& getLidarTopic() const { return m_strLidTopic; }
    const std::string& getImuTopic() const { return m_strImuTopic; }
    const std::string& getGnssTopic() const { return m_strGnssTopic; }
    const std::string& getGnssHeadingTopic() const { return m_strGnssHeadingTopic; }
    const std::string& getLegTopic() const { return m_strLegTopic; }
    const std::string& getManualPosTopic() const { return m_strManualPosTopic; }

    // Time sync getter methods
    bool isTimeSyncEnabled() const { return m_bTimeSyncEn; }
    double getTimeDiffLidarToImu() const { return m_dTimeDiffLidarToImu; }

    // Filter sizes getter methods
    double getFilterSizeCornerMin() const { return m_dFilterSizeCornerMin; }
    double getFilterSizeSurfMin() const { return m_dFilterSizeSurfMin; }
    double getFilterSizeMapMin() const { return m_dFilterSizeMapMin; }

    // Mapping parameters getter methods
    double getCubeLength() const { return m_dCubeLen; }
    float getDetRange() const { return m_fDetRange; }
    double getFovDegree() const { return m_dFovDeg; }
    double getGyrCov() const { return m_dGyrCov; }
    double getAccCov() const { return m_dAccCov; }
    double getBGyrCov() const { return m_dBGyrCov; }
    double getBAccCov() const { return m_dBAccCov; }

    // Preprocessing parameters getter methods
    double getBlind() const { return m_dBlind; }
    int getLidarType() const { return m_nLidarType; }
    int getScans() const { return m_nScans; }
    int getTimeUnit() const { return m_nTimeUnit; }
    int getScanRate() const { return m_nScanRate; }
    int getPointFilterNum() const { return m_nPointFilterNum; }
    bool isFeatureEnabled() const { return m_bFeatureEnabled; }

    // Extrinsic parameters getter methods
    bool isExtrinsicEstEnabled() const { return m_bExtrinsicEstEn; }
    const std::vector<double>& getExtrinT() const { return m_vecExtrinT; }
    const std::vector<double>& getExtrinR() const { return m_vecExtrinR; }
    const std::vector<double>& getRtk2LidarT() const { return m_vecRtk2LidarT; }

    // Reloc parameters getter methods
    double getSearchRadius() const { return m_dSearchRadius; }
    double getSearchRadiusRtk() const { return m_dSearchRadiusRtk; }

    // RTK parameters getter methods
    bool isRtkEnabled() const { return m_bUseRtk; }
    int getStatusThreshold() const { return m_nStatusThreshold; }

    // IMU parameters getter methods
    const std::string& getImuAxis() const { return m_strImuAxis; }

    // Zero velocity detection getter methods
    double getAccStdThreshold() const { return m_dAccStdThreshold; }
    double getGyroStdThreshold() const { return m_dGyroStdThreshold; }
    bool isZeroDetectEnabled() const { return m_bZeroDetectEnable; }

    // Matching parameters getter methods
    double getMatchRate() const { return m_dMatchRate; }

    // reloc parameters getter methods
    double getMatchRateThreshold() const { return m_dMatchRateThreshold; }
    double getLocalMapRadius() const { return m_dLocalMapRadius; }

    bool isInitialized() const { return m_bInitialized; }

private:
    Config() : m_bInitialized(false) {} // 私有构造函数
    
    void loadParameters(const std::string& config_file);
    template<typename T>
    T getParam(const YAML::Node& node, const std::string& key, const T& default_value);

    bool m_bInitialized;

    // Member variables
    std::string m_strParentDir;
    std::string m_strMapDir;
    std::string m_strMapName;
    std::string m_strPoseTxtName;
    std::string m_strLoadMapPath;
    std::string m_strLoadPosePath;
    std::string m_strLog4cplusFilePath;
    std::string m_strLog4cplusOutfilePath;
    bool m_bPathEn;
    bool m_bScanPubEn;
    bool m_bDensePubEn;
    bool m_bScanBodyPubEn;
    bool m_bVisualIkdtree;
    int m_nNumMaxIterations;
    std::string m_strMapFilePath;
    std::string m_strLidTopic;
    std::string m_strImuTopic;
    std::string m_strGnssTopic;
    std::string m_strGnssHeadingTopic;
    std::string m_strLegTopic;
    std::string m_strManualPosTopic;
    bool m_bTimeSyncEn;
    double m_dTimeDiffLidarToImu;
    double m_dFilterSizeCornerMin;
    double m_dFilterSizeSurfMin;
    double m_dFilterSizeMapMin;
    double m_dCubeLen;
    float m_fDetRange;
    double m_dFovDeg;
    double m_dGyrCov;
    double m_dAccCov;
    double m_dBGyrCov;
    double m_dBAccCov;
    double m_dBlind;
    int m_nLidarType;
    int m_nScans;
    int m_nTimeUnit;
    int m_nScanRate;
    int m_nPointFilterNum;
    bool m_bFeatureEnabled;
    bool m_bExtrinsicEstEn;
    std::vector<double> m_vecExtrinT;
    std::vector<double> m_vecExtrinR;
    std::vector<double> m_vecRtk2LidarT;
    double m_dSearchRadius;
    double m_dSearchRadiusRtk;
    bool m_bUseRtk;
    int m_nStatusThreshold;
    std::string m_strImuAxis;
    double m_dAccStdThreshold;
    double m_dGyroStdThreshold;
    bool m_bZeroDetectEnable;

    double m_dMatchRate;

    double m_dMatchRateThreshold;
    double m_dLocalMapRadius;
};
#endif