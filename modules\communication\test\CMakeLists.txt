# communication/CMakeLists.txt
cmake_minimum_required(VERSION 2.8.3)
project(communication_test)

# 全局设置库文件输出目录
# set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
# set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)

set(CMAKE_CXX_STANDARD 17)
#set(CMAKE_CXX_FLAGS "-std=c++17 -march=native")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall")

if (NOT DEFINED CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE "Debug")
endif()

if(NOT DEFINED PLATFORM)
  set(PLATFORM "x86_64")
endif()

find_package(PCL REQUIRED)

include_directories(
  /usr/include/opencv4/
  ${PCL_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/../../communication/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/include
)

add_executable(communication_test
  communication_node.cpp
)

target_link_directories(communication_test
  PUBLIC
  ${CMAKE_CURRENT_SOURCE_DIR}/../../communication/lib
)
target_link_libraries(communication_test
  communication_core
  pthread

)



