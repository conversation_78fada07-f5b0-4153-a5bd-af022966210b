# perception/CMakeLists.txt
cmake_minimum_required(VERSION 2.8.3)
project(controller)

# 全局设置库文件输出目录
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)

#set(CMAKE_BUILD_TYPE "Release")
set(CMAKE_BUILD_TYPE "Debug")
set(CMAKE_CXX_STANDARD 14)
#set(CMAKE_CXX_FLAGS "-std=c++17 -march=native")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall")

# 查找Eigen库
find_package(Eigen3 REQUIRED)
if(NOT EIGEN3_FOUND)
    message(FATAL_ERROR "Eigen3 not found. Please install Eigen3.")
endif()

# 查找PCL库
find_package(PCL REQUIRED)
if(NOT PCL_FOUND)
    message(FATAL_ERROR "PCL not found. Please install PCL.")
endif()

# find_package(OpenCV REQUIRED)



include_directories(
    include
    # ${OpenCV_INCLUDE_DIRS}
    ${CMAKE_CURRENT_SOURCE_DIR}/../communication/include 
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src/yocs_velocity_smoother
    ${CMAKE_CURRENT_SOURCE_DIR}/src/calibration_control
    ${EIGEN3_INCLUDE_DIR}
    ${PCL_INCLUDE_DIRS}
)

# 添加velocity_smoother库
add_library(velocity_smoother
    src/yocs_velocity_smoother/velocity_smoother.cpp
)

# 添加calibration库
add_library(calibration
    src/calibration_control/calibration.cpp
)

# 添加pid_controller库
add_library(pid_controller
    src/calibration_control/pid_controller.cpp
)

# 添加controller核心库
add_library(${PROJECT_NAME}_core
    src/controller.cpp
)

# 添加velocity_smoother_node可执行文件
add_executable(velocity_smoother_node
    src/yocs_velocity_smoother/velocity_smoother_node.cpp
)

# 添加velocity_smoother_test可执行文件
add_executable(velocity_smoother_test
    src/yocs_velocity_smoother/velocity_smoother_test.cpp
)

# 添加calibration_node可执行文件
add_executable(calibration_node
    src/calibration_control/calibration_node.cpp
)

# 添加calibration_example可执行文件
add_executable(calibration_example
    src/calibration_control/calibrationExample.cpp
)

# 添加pid_controller_test可执行文件
add_executable(pid_controller_test
    src/calibration_control/pid_controller_test.cpp
)

# 添加controller_node可执行文件
add_executable(${PROJECT_NAME}_node
    src/controller_node.cpp
)

# 设置RPATH
set(CMAKE_SKIP_BUILD_RPATH FALSE)
set(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
set(CMAKE_INSTALL_RPATH "${CMAKE_CURRENT_SOURCE_DIR}/../common/lib:${CMAKE_CURRENT_SOURCE_DIR}/../communication/lib")

# 查找pthread库
find_package(Threads REQUIRED)

# 查找库文件
find_library(COMMON_LIB common_lib
    PATHS
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/lib
    NO_DEFAULT_PATH
)

find_library(COMMUNICATION_CORE communication_core
    PATHS
    ${CMAKE_CURRENT_SOURCE_DIR}/../communication/lib
    NO_DEFAULT_PATH
)

if(NOT COMMON_LIB)
    message(FATAL_ERROR "common_lib not found")
endif()

if(NOT COMMUNICATION_CORE)
    message(FATAL_ERROR "communication_core not found")
endif()

# 设置链接目录
link_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../communication/lib
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/lib
    ${CMAKE_CURRENT_SOURCE_DIR}/lib
    ${PCL_LIBRARY_DIRS}
)

# 链接velocity_smoother_node
target_link_libraries(velocity_smoother_node
    velocity_smoother
    ${COMMON_LIB}
    ${COMMUNICATION_CORE}
    ${PROJECT_NAME}_core
    Threads::Threads
)

# 链接velocity_smoother_test
target_link_libraries(velocity_smoother_test
    velocity_smoother
    ${COMMON_LIB}
    ${COMMUNICATION_CORE}
    ${PROJECT_NAME}_core
    Threads::Threads
)

# 链接calibration_node
target_link_libraries(calibration_node
    calibration
    pid_controller
    ${COMMON_LIB}
    ${COMMUNICATION_CORE}
    ${PROJECT_NAME}_core
    ${PCL_LIBRARIES}
    Threads::Threads
)

# 链接calibration_example
target_link_libraries(calibration_example
    calibration
    pid_controller
    ${COMMON_LIB}
    ${COMMUNICATION_CORE}
    ${PROJECT_NAME}_core
    ${PCL_LIBRARIES}
    Threads::Threads
)

# 链接pid_controller_test
target_link_libraries(pid_controller_test
    pid_controller
    ${COMMON_LIB}
    ${COMMUNICATION_CORE}
    ${PROJECT_NAME}_core
    ${PCL_LIBRARIES}
    Threads::Threads
)

# 链接controller_node
target_link_libraries(${PROJECT_NAME}_node 
    ${COMMON_LIB}
    ${COMMUNICATION_CORE}
    ${PROJECT_NAME}_core   
    Threads::Threads
)

# 安装配置文件
install(FILES 
    src/yocs_velocity_smoother/velocity_smoother_config.yaml
    src/calibration_control/calibration.yaml
    DESTINATION ${CMAKE_INSTALL_PREFIX}/config
)




