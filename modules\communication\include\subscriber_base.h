/**
 * @file odometry_subscriber_base.h
 * @brief This file contains the base class for subscribing to odometry data.
 * C++模板类
 */
#pragma once

#include <deque>
#include <mutex>
#include <opencv2/opencv.hpp>
#include "data_struct.h"

namespace communication{

// using LidarDataCallback = std::function<void(const CloudData&)>;
template<typename T>
class SubscriberBase {
    public:
        SubscriberBase(const std::string &topic, size_t max_buffer_size)
            : topic_(topic), max_buffer_size_(max_buffer_size) {

            }
        virtual ~SubscriberBase() = default;

        std::deque<T> GetBuffer(bool clear_buffer = true){
            std::lock_guard<std::mutex> lock(buffer_mutex_);
            if(!clear_buffer)
            {
                return data_buffer_;
            }
            else
            {
                std::deque<T> result;
                result.swap(data_buffer_);
                return result;
            }
        }

        bool IsBufferEmpty() const
        {
            return data_buffer_.empty();
        }

        T GetBufferFront(){
            std::lock_guard<std::mutex> lock(buffer_mutex_);
            auto result = data_buffer_.front();
            data_buffer_.pop_front();
            return result;
        }

    protected:

        std::deque<T> data_buffer_;
        std::mutex buffer_mutex_;
        size_t max_buffer_size_ = 20;  // Adjust as needed
        std::string topic_;

        // LidarDataCallback lidar_data_callback_ = nullptr; // Callback function for IMU data
    };

    using IntDataSubscriberBase = SubscriberBase<int>;
    using DoubleDataSubscriberBase = SubscriberBase<double>;
    using StringDataSubscriberBase = SubscriberBase<std::string>;
    using BoolDataSubscriberBase = SubscriberBase<bool>;

    using ImuDataSubscriberBase = SubscriberBase<IMUData>;
    using GnssDataSubscriberBase = SubscriberBase<GNSSData>;
    using LidarDataSubscriberBase = SubscriberBase<CloudXYZRIData>;
    using CloudDataSubscriberBase = SubscriberBase<CloudData>;
    using OdometrySubscriberBase = SubscriberBase<PoseVelData>;
    using PathDataSubscriberBase = SubscriberBase<PathData>;
    using PoseDataSubscriberBase = SubscriberBase<PoseData>;

    using ImageDataSubscriberBase = SubscriberBase< cv::Mat>;// camera data subscriber
    using CameraIntSubscriberBase = SubscriberBase<CameraInfo>; // camera intrinsics data subscriber
    using CameraExtSubscriberBase = SubscriberBase<TransformExtrinsics>; // camera extrinsics data subscriber

} // namespace communication