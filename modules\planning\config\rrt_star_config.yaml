# RRT* Global Planner Configuration File
# 去ROS化RRT*全局路径规划器配置文件

# 搜索参数配置
search_config:
  search_radius: 1.0          # 搜索半径 (米)
  goal_radius: 0.2            # 目标半径 (米)
  epsilon_min: 0.001          # 最小扩展距离 (米)
  epsilon_max: 0.1            # 最大扩展距离 (米)
  max_nodes_num: 2000000      # 最大节点数
  plan_timeout: 10.0          # 规划超时时间 (秒)

# 路径优化参数配置
path_config:
  path_point_spacing: 0.25    # 路径点间距 (米)
  angle_difference: 0.157     # 角度差异阈值 (弧度, π/20)

# 采样参数配置
sampling_config:
  goal_bias_probability: 0.2  # 目标偏向采样概率 (0.0-1.0)
  use_informed_sampling: false # 是否使用信息采样
  use_bidirectional_search: true # 是否使用双向搜索

# 地图参数配置
map_config:
  resolution: 0.05            # 地图分辨率 (米/像素)
  frame_id: "map"             # 坐标系ID

# 运行时参数配置
runtime_config:
  planning_frequency: 10      # 规划频率 (Hz)
  enable_visualization: true  # 是否启用可视化
  enable_debug: false         # 是否启用调试输出

# 调试参数配置
debug_config:
  print_timing: true          # 是否打印时间信息
  save_tree_data: false       # 是否保存搜索树数据
  debug_output_path: "/tmp/rrt_star_debug" # 调试输出路径

# 预设配置方案
presets:
  # 快速规划方案 - 适用于实时应用
  fast:
    search_config:
      max_nodes_num: 5000
      plan_timeout: 2.0
      search_radius: 0.8
      goal_radius: 0.3
    sampling_config:
      goal_bias_probability: 0.3
      use_bidirectional_search: true
    
  # 标准规划方案 - 平衡性能和质量
  standard:
    search_config:
      max_nodes_num: 10000
      plan_timeout: 5.0
      search_radius: 1.0
      goal_radius: 0.2
    sampling_config:
      goal_bias_probability: 0.2
      use_bidirectional_search: true
    
  # 精确规划方案 - 追求最优路径
  precise:
    search_config:
      max_nodes_num: 50000
      plan_timeout: 15.0
      search_radius: 1.5
      goal_radius: 0.15
    sampling_config:
      goal_bias_probability: 0.1
      use_bidirectional_search: true
      use_informed_sampling: true
    path_config:
      path_point_spacing: 0.1
      angle_difference: 0.087  # π/36

# 环境特定配置
environments:
  # 室内环境
  indoor:
    search_config:
      search_radius: 0.8
      goal_radius: 0.15
      epsilon_max: 0.05
    path_config:
      path_point_spacing: 0.15
      
  # 室外环境
  outdoor:
    search_config:
      search_radius: 2.0
      goal_radius: 0.5
      epsilon_max: 0.2
    path_config:
      path_point_spacing: 0.5
      
  # 狭窄空间
  narrow:
    search_config:
      search_radius: 0.5
      goal_radius: 0.1
      epsilon_max: 0.03
    path_config:
      path_point_spacing: 0.1
      angle_difference: 0.052  # π/60

# 机器人特定配置
robots:
  # 小型机器人
  small_robot:
    search_config:
      search_radius: 0.6
      goal_radius: 0.1
    path_config:
      path_point_spacing: 0.1
      
  # 中型机器人
  medium_robot:
    search_config:
      search_radius: 1.0
      goal_radius: 0.2
    path_config:
      path_point_spacing: 0.2
      
  # 大型机器人
  large_robot:
    search_config:
      search_radius: 1.5
      goal_radius: 0.3
    path_config:
      path_point_spacing: 0.3

# 性能调优配置
performance:
  # 高性能配置
  high_performance:
    search_config:
      max_nodes_num: 20000
      plan_timeout: 8.0
    runtime_config:
      planning_frequency: 20
      enable_visualization: false
      
  # 低功耗配置
  low_power:
    search_config:
      max_nodes_num: 3000
      plan_timeout: 3.0
    runtime_config:
      planning_frequency: 5
      enable_visualization: false

# 使用说明
usage_notes: |
  1. 选择合适的预设配置作为起点
  2. 根据具体环境调整参数
  3. 根据机器人特性微调配置
  4. 在实际使用中监控性能指标
  5. 必要时进行参数优化

# 参数说明
parameter_descriptions:
  search_radius: "RRT*重连和父节点选择的搜索半径，影响路径质量"
  goal_radius: "到达目标的容差半径，影响规划成功率"
  epsilon_min: "最小扩展距离，防止节点过于密集"
  epsilon_max: "最大扩展距离，控制树的扩展步长"
  max_nodes_num: "最大节点数，限制搜索规模"
  plan_timeout: "规划超时时间，平衡质量和实时性"
  goal_bias_probability: "目标偏向采样概率，影响收敛速度"
  path_point_spacing: "路径点间距，影响路径平滑度"
  angle_difference: "角度差异阈值，用于路径优化"
