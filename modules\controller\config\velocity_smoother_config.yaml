# Velocity Smoother Configuration File
# 去ROS化速度平滑器配置文件

# 基本速度限制参数
speed_lim_v: 1.0          # 最大线性速度 (m/s)
speed_lim_w: 1.0          # 最大角速度 (rad/s)

# 加速度限制参数
accel_lim_v: 1.0          # 线性加速度限制 (m/s²)
accel_lim_w: 1.0          # 角加速度限制 (rad/s²)

# 减速控制参数
decel_factor: 1.0         # 减速因子 (减速度 = 减速因子 × 加速度)

# 控制参数
frequency: 20.0           # 控制频率 (Hz)
quiet: false              # 静默模式 (true/false)
robot_feedback: 0         # 机器人反馈类型 (0=NONE, 1=ODOMETRY, 2=COMMANDS)

# 预设配置方案
presets:
  # 保守配置 - 适用于精确操作
  conservative:
    speed_lim_v: 0.5
    speed_lim_w: 0.3
    accel_lim_v: 0.3
    accel_lim_w: 0.2
    decel_factor: 1.5
    frequency: 30.0
    
  # 标准配置 - 平衡性能和安全
  standard:
    speed_lim_v: 1.0
    speed_lim_w: 1.0
    accel_lim_v: 1.0
    accel_lim_w: 1.0
    decel_factor: 2.0
    frequency: 20.0
    
  # 激进配置 - 追求高性能
  aggressive:
    speed_lim_v: 2.0
    speed_lim_w: 1.5
    accel_lim_v: 2.0
    accel_lim_w: 1.8
    decel_factor: 3.0
    frequency: 50.0
    
  # 紧急配置 - 快速停止
  emergency:
    speed_lim_v: 0.1
    speed_lim_w: 0.1
    accel_lim_v: 0.5
    accel_lim_w: 0.5
    decel_factor: 5.0
    frequency: 100.0

# 机器人类型特定配置
robot_types:
  # 小型室内机器人
  small_indoor:
    speed_lim_v: 0.8
    speed_lim_w: 1.2
    accel_lim_v: 0.6
    accel_lim_w: 0.8
    decel_factor: 1.8
    frequency: 30.0
    robot_feedback: 1  # 使用里程计反馈
    
  # 中型服务机器人
  medium_service:
    speed_lim_v: 1.5
    speed_lim_w: 1.0
    accel_lim_v: 1.2
    accel_lim_w: 0.8
    decel_factor: 2.2
    frequency: 25.0
    robot_feedback: 1
    
  # 大型工业机器人
  large_industrial:
    speed_lim_v: 2.5
    speed_lim_w: 0.8
    accel_lim_v: 1.8
    accel_lim_w: 0.6
    decel_factor: 2.8
    frequency: 20.0
    robot_feedback: 2  # 使用命令反馈
    
  # 高速移动机器人
  high_speed:
    speed_lim_v: 5.0
    speed_lim_w: 2.0
    accel_lim_v: 3.0
    accel_lim_w: 2.5
    decel_factor: 4.0
    frequency: 100.0
    robot_feedback: 1

# 环境特定配置
environments:
  # 室内环境
  indoor:
    speed_lim_v: 1.0
    speed_lim_w: 1.2
    accel_lim_v: 0.8
    accel_lim_w: 1.0
    decel_factor: 2.0
    
  # 室外环境
  outdoor:
    speed_lim_v: 2.0
    speed_lim_w: 1.0
    accel_lim_v: 1.5
    accel_lim_w: 0.8
    decel_factor: 2.5
    
  # 狭窄空间
  narrow_space:
    speed_lim_v: 0.3
    speed_lim_w: 0.5
    accel_lim_v: 0.2
    accel_lim_w: 0.3
    decel_factor: 1.5
    frequency: 50.0
    
  # 开阔空间
  open_space:
    speed_lim_v: 3.0
    speed_lim_w: 1.5
    accel_lim_v: 2.5
    accel_lim_w: 1.2
    decel_factor: 3.0
    frequency: 15.0

# 任务特定配置
tasks:
  # 导航任务
  navigation:
    speed_lim_v: 1.5
    speed_lim_w: 1.0
    accel_lim_v: 1.2
    accel_lim_w: 0.8
    decel_factor: 2.0
    robot_feedback: 1
    
  # 跟随任务
  following:
    speed_lim_v: 1.0
    speed_lim_w: 1.5
    accel_lim_v: 1.5
    accel_lim_w: 1.2
    decel_factor: 2.5
    frequency: 30.0
    
  # 巡逻任务
  patrol:
    speed_lim_v: 0.8
    speed_lim_w: 0.6
    accel_lim_v: 0.6
    accel_lim_w: 0.4
    decel_factor: 1.8
    frequency: 20.0
    
  # 搬运任务
  transport:
    speed_lim_v: 0.6
    speed_lim_w: 0.4
    accel_lim_v: 0.4
    accel_lim_w: 0.3
    decel_factor: 1.5
    frequency: 25.0

# 性能调优配置
performance:
  # 高性能配置
  high_performance:
    frequency: 100.0
    quiet: true
    robot_feedback: 2
    
  # 低功耗配置
  low_power:
    frequency: 10.0
    quiet: true
    robot_feedback: 0
    
  # 实时配置
  real_time:
    frequency: 200.0
    quiet: true
    robot_feedback: 1

# 安全配置
safety:
  # 最大安全限制
  max_limits:
    speed_lim_v: 0.5
    speed_lim_w: 0.3
    accel_lim_v: 0.3
    accel_lim_w: 0.2
    decel_factor: 1.2
    
  # 紧急停止配置
  emergency_stop:
    speed_lim_v: 0.0
    speed_lim_w: 0.0
    accel_lim_v: 0.1
    accel_lim_w: 0.1
    decel_factor: 10.0
    frequency: 100.0

# 调试配置
debug:
  # 详细调试
  verbose:
    quiet: false
    frequency: 50.0
    
  # 性能分析
  profiling:
    frequency: 1000.0
    quiet: true

# 使用说明
usage_notes: |
  1. 根据机器人类型选择合适的预设配置
  2. 根据环境和任务调整参数
  3. 在实际使用中监控性能指标
  4. 根据需要进行参数微调
  5. 注意安全限制的设置

# 参数说明
parameter_descriptions:
  speed_lim_v: "最大线性速度限制，影响机器人的最大移动速度"
  speed_lim_w: "最大角速度限制，影响机器人的最大旋转速度"
  accel_lim_v: "线性加速度限制，控制速度变化的平滑程度"
  accel_lim_w: "角加速度限制，控制旋转速度变化的平滑程度"
  decel_factor: "减速因子，减速度 = 减速因子 × 加速度"
  frequency: "控制频率，影响响应速度和CPU占用"
  quiet: "静默模式，禁用非关键的调试输出"
  robot_feedback: "机器人反馈类型，影响速度控制的精度"

# 配置验证规则
validation_rules:
  speed_lim_v: 
    min: 0.0
    max: 10.0
    unit: "m/s"
  speed_lim_w:
    min: 0.0
    max: 5.0
    unit: "rad/s"
  accel_lim_v:
    min: 0.1
    max: 10.0
    unit: "m/s²"
  accel_lim_w:
    min: 0.1
    max: 10.0
    unit: "rad/s²"
  decel_factor:
    min: 0.5
    max: 10.0
    unit: "dimensionless"
  frequency:
    min: 1.0
    max: 1000.0
    unit: "Hz"
  robot_feedback:
    min: 0
    max: 2
    unit: "enum"
