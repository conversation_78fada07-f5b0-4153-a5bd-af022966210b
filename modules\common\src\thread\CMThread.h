#ifndef _CM_THREAD_H_
#define _CM_THREAD_H_

#include "CM_pubhead.h"

#include "CMRWLock.h"
#include "CMWork.h"
#include "CMEvent.h"

#ifdef LINUX
#include <unistd.h>
#include<pthread.h>
#endif

enum CM_THREAD_STATE
{
    CM_TS_TERMINATED = 0, 
    CM_TS_WAITING, 
    CM_TS_RUNNING,
};

class CMThread
{
public:
    CMThread();
    ~CMThread();

public:
    cx_int AssignWork(CMWork* pWork);
    ThreadProc* GetThreadProc();
    LPVOID  GetContext();

    cx_int Create(ThreadProc pfnThreadProc, LPVOID const pContext);
#ifdef LINUX
#endif

public:
    cx_int Start();
    cx_int Pause();
    cx_int Resume();
    cx_int Stop();

public:
    cx_int SetState(CM_THREAD_STATE eState);
    CM_THREAD_STATE GetState();

    CMWork* GetWork();

    const cx_bool IsTerminate();
    cx_int Reset();
    cx_int FinishWork();

public:
    cx_int GetPriority(int& priority) const;
    cx_int SetPriority(const int iPriority);

private:
    CMWork* m_pWork;
    CM_THREAD_STATE m_eState;
    CMEvent m_TerminateEvent;
    CMEvent m_FinishedEvent;
    CMEvent m_RunEvent;

#ifdef LINUX

    pthread_t m_pthread;
    pthread_attr_t m_attr;

#endif

};

class CMThreadSet : public std::set<CMThread*>, public CMRWLock
{

};

cx_int SleepS(cx_dword dwMilliseconds);
cx_int SleepMS(cx_dword dwMilliseconds);
cx_int SleepUS(cx_dword dwMilliseconds);

#endif // !_CM_THREAD_H_

