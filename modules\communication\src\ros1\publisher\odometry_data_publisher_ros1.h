#pragma once

#if COMMUNICATION_TYPE == ROS1

#include <ros/ros.h>
#include <nav_msgs/Odometry.h>

#include "publisher_base.h"

namespace communication::ros1{

class OdometryDataPublisherRos1 :  public OdometryPublisherBase{
    public:
    OdometryDataPublisherRos1(ros::NodeHandle &nh, 
            const std::string &topic, 
            const std::string &frame_id = "map",
            const std::string &child_frame_id = "body",
            size_t max_buffer_size = 10);

        virtual ~OdometryDataPublisherRos1() = default;

    protected:
        virtual void PublishMsg() override
        {
           
            publisher_.publish(msg_);
        }

        virtual void ToMsg() override
        {
            msg_.header.stamp = ros::Time(data_.pose_data.time);
            msg_.pose.pose.position.x = data_.pose_data.position(0);
            msg_.pose.pose.position.y = data_.pose_data.position(1);
            msg_.pose.pose.position.z = data_.pose_data.position(2);

            msg_.pose.pose.orientation.x = data_.pose_data.orientation(0);
            msg_.pose.pose.orientation.y = data_.pose_data.orientation(1);
            msg_.pose.pose.orientation.z = data_.pose_data.orientation(2);
            msg_.pose.pose.orientation.w = data_.pose_data.orientation(3);

            msg_.twist.twist.linear.x = data_.vel(0);
            msg_.twist.twist.linear.y = data_.vel(1);
            msg_.twist.twist.linear.z = data_.vel(2);
            msg_.twist.twist.angular.x = data_.angle_vel(0);
            msg_.twist.twist.angular.y = data_.angle_vel(1);
            msg_.twist.twist.angular.z = data_.angle_vel(2);

            // Set the frame IDs for the odometry message
            msg_.header.frame_id = frame_id_;
            msg_.child_frame_id = child_frame_id_; // Set the child frame ID for the odometry message
        }

        virtual int GetSubscriberCount() const override {
            return publisher_.getNumSubscribers();
        }
    private:
        ros::NodeHandle& nh_;
        ros::Publisher publisher_;
        nav_msgs::Odometry msg_; // ROS message type for odometry data
        std::string frame_id_;
        std::string child_frame_id_; // Child frame ID for the odometry message
    };

} // namespace communication::ros1{

#endif