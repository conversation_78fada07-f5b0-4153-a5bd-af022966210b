﻿
#include "CMMutex.h"

CMMutex::CMMutex()
{
#ifdef WIN32
    InitializeCriticalSection(&m_cs);
#else
    pthread_mutex_init(&m_mutex, NULL);
#endif

    SetRecursiveAttr();
}

CMMutex::~CMMutex()
{
#ifdef WIN32
    DeleteCriticalSection(&m_cs);
#else
    pthread_mutex_destroy(&m_mutex);
#endif
}

cx_int CMMutex::Lock()
{
#ifdef WIN32
    EnterCriticalSection(&m_cs);
#else
    pthread_mutex_lock(&m_mutex);
#endif

    return 0;
}

cx_int CMMutex::Unlock()
{
#ifdef WIN32
    LeaveCriticalSection(&m_cs);
#else
    pthread_mutex_unlock(&m_mutex);
#endif

    return 0;
}

cx_int CMMutex::SetRecursiveAttr()
{
#ifndef WIN32
    pthread_mutexattr_t mattr;
    pthread_mutexattr_init(&mattr);
    pthread_mutexattr_settype(&mattr, PTHREAD_MUTEX_RECURSIVE_NP);

    pthread_mutex_init(&m_mutex, &mattr);

    pthread_mutexattr_destroy(&mattr);
#endif

    return 0;
}
