#ifndef PID_CONTROLLER_H
#define PID_CONTROLLER_H

#include <algorithm>
#include <cmath>

/**
 * @brief PID控制器类 - 完全保留原有实现
 * 
 * 实现了比例-积分-微分控制算法，包含积分限幅、死区处理等功能
 */
class PIDController
{
public:
    /**
     * @brief 构造函数
     * @param kp 比例增益
     * @param ki 积分增益  
     * @param kd 微分增益
     */
    PIDController(double kp, double ki, double kd);
    
    /**
     * @brief 默认构造函数
     */
    PIDController();
    
    /**
     * @brief 析构函数
     */
    ~PIDController();
    
    /**
     * @brief 设置PID参数
     * @param Kp 比例增益
     * @param Ki 积分增益
     * @param Kd 微分增益
     */
    void Parameter(double Kp, double Ki, double Kd);
    
    /**
     * @brief 计算PID控制量 (完全保留原有实现)
     * @param error 误差值
     * @param dt 时间间隔 (当前实现中固定为0.01)
     * @param actual 实际值 (当前实现中未使用)
     * @param err_max 误差限幅值
     * @param limit 输出限幅值
     * @return 控制输出
     */
    double Control(double error, double dt, double actual, double err_max, double limit);
    
    /**
     * @brief 重置PID控制器状态
     */
    void reset();
    
    /**
     * @brief 获取当前PID参数
     */
    void getParameters(double& kp, double& ki, double& kd) const;
    
    /**
     * @brief 获取当前积分值
     */
    double getIntegral() const;
    
    /**
     * @brief 获取上一次误差值
     */
    double getPreviousError() const;
    
    /**
     * @brief 设置积分限幅值
     * @param integral_limit 积分限幅值
     */
    void setIntegralLimit(double integral_limit);
    
    /**
     * @brief 设置死区值
     * @param deadzone 死区值
     */
    void setDeadzone(double deadzone);

private:
    // PID参数
    double kp_;         ///< 比例增益
    double ki_;         ///< 积分增益
    double kd_;         ///< 微分增益
    
    // 状态变量
    double prev_error_; ///< 上一次误差
    double integral_;   ///< 积分累积值
    
    // 限制参数
    double integral_limit_;  ///< 积分限幅值 (默认为无限大)
    double deadzone_;        ///< 死区值 (默认为0.001)
    
    // 常量
    static constexpr double DEFAULT_DT = 0.01;      ///< 默认时间间隔
    static constexpr double DEFAULT_DEADZONE = 0.001; ///< 默认死区值
};

#endif // PID_CONTROLLER_H
