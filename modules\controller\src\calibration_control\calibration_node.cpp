#include "calibration.h"
#include <iostream>
#include <chrono>
#include <thread>

using namespace calibration_no_ros;

/**
 * @brief 校准节点 - 基础节点创建和初始化
 */
int main(int argc, char** argv) {
    std::cout << "=== CalibrationNoRos 基础节点程序 ===" << std::endl;

    try {
        // Create calibration controller
        CalibrationNoRos calibrator("calibration_node");
        
        // Initialize
        if (!calibrator.init()) {
            std::cerr << "Failed to initialize calibration controller" << std::endl;
            return -1;
        }
        
        // Set callbacks
        calibrator.setSpeedPublishCallback([](const std::shared_ptr<Twist>& twist) {
            std::cout << "Speed: linear_x=" << twist->linear_x 
                      << ", linear_y=" << twist->linear_y 
                      << ", angular_z=" << twist->angular_z << std::endl;
        });
        
        calibrator.setStopPublishCallback([](const std::shared_ptr<Int8Msg>& msg) {
            std::cout << "Stop signal: " << static_cast<int>(msg->data) << std::endl;
        });
        
        calibrator.setInnerStopPublishCallback([](const std::shared_ptr<Int8Msg>& msg) {
            std::cout << "Inner stop signal: " << static_cast<int>(msg->data) << std::endl;
        });
        
        calibrator.setModePublishCallback([](const std::shared_ptr<BoolMsg>& msg) {
            std::cout << "Mode: " << (msg->data ? "true" : "false") << std::endl;
        });
        
        // Set parameters
        calibrator.setPIDParameters(1.0, 0.1, 0.01,  // yaw PID
                                    1.0, 0.1, 0.01,  // x PID
                                    1.0, 0.1, 0.01); // y PID
                                    
        calibrator.setErrorLimits(0.5, -0.5, 0.5, 0.5);  // yaw_max, yaw_min, x_max, y_max
        calibrator.setVelocityLimits(1.0, 1.0, 1.0);     // x_max, y_max, yaw_max
        calibrator.setPrecision(0.1, 0.1, 0.1);          // yaw_precision, x_precision, y_precision
        
        // Start the controller
        calibrator.start();
        
        // Main loop
        while (calibrator.isRunning()) {
            // Print status
           // calibrator.printStatus();
            
            // Sleep
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // Stop the controller
        calibrator.stop();
        
        std::cout << "\n✅ 校准节点启动完成" << std::endl;
        std::cout << "节点状态: " << (calibrator.isRunning() ? "运行中" : "已停止") << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "❌ 程序执行出错: " << e.what() << std::endl;
        return -1;
    }

    return 0;
}
