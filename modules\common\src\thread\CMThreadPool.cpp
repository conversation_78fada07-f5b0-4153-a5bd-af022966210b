#include "CMThreadPool.h"

#include "CMThread.h"
#include "CMWork.h"
#include "CMSingleton.h"

CMThreadPool* GetSingleton4ThreadPool()
{
    static CMSingleton<CMThreadPool>   s_ThreadPool;
    
    return s_ThreadPool.GetSingletonInstance();
}

cx_bool CMThreadPool::g_bInitializedThreadPool = false;

CMThreadPool::CMThreadPool()
{
    m_dwThreadMinimum = 0;
    m_dwThreadMaximum = 0;
    g_bInitializedThreadPool = false;

#ifdef WIN32
    m_pCleanupGroup = NULL;
    m_pThreadPool = NULL;
#endif // WIN32
       
    m_TerminateEvent.Create();

    Create();
}

CMThreadPool::~CMThreadPool()
{
    Close();

    (void)m_TerminateEvent.Destroy();

}

cx_int CMThreadPool::Create()
{
    m_TerminateEvent.Reset();

#ifdef WIN32
    //��Ч�Լ���
    if (m_pThreadPool != NULL) 
    {
        Close();
        m_TerminateEvent.Reset();
    }

    //����˽���̳߳�
    m_pThreadPool = CreateThreadpool(NULL);
    if (m_pThreadPool == NULL) 
    {
        MessageBox(NULL, TEXT("�޷�����˽���̳߳أ�"), TEXT("��ʼ��ʧ�ܣ�"), MB_OK  | MB_ICONERROR);
        return -1;
    }

    //�����̳߳أ���С�߳���Ϊ2�����Ϊ4.
    SetThreadpoolThreadMaximum(m_pThreadPool, 4);
    if (!SetThreadpoolThreadMinimum(m_pThreadPool, 2)) 
    {
        MessageBox(NULL, TEXT("�����̳߳���С�߳���ʧ�ܣ�"), TEXT("��ʼ��ʧ�ܣ�"), MB_OK  | MB_ICONERROR);
        return -1;
    }

    //���ûص�������
    //ע�⣺����һ�����������������Ժ���ֶ�����
    //    CallbackEnviron->Version =1;
    //    CallbackEnviron->Pool    = NULL;
    //    CallbackEnviron->CleanupGroup = NULL;
    //    CallbackEnviron->CleanupGroupCancelCallback = NULL;
    //    CallbackEnviron->RaceDll = NULL;
    //    CallbackEnviron->ActivationContext = NULL;
    //    CallbackEnviron->FinalizationCallback = NULL;
    //    CallbackEnviron->u.Flags = 0;
    //��ʼ���ص�������
    InitializeThreadpoolEnvironment(&m_cbEnv);

    //���̳߳���ص���������
    //ע�⣺�õ��û�ʹ��CallbackEnviro->Pool = Pool;
    SetThreadpoolCallbackPool(&m_cbEnv, m_pThreadPool);

    //������Դ������
    m_pCleanupGroup = CreateThreadpoolCleanupGroup();
    //���������뻷�������
    SetThreadpoolCallbackCleanupGroup(&m_cbEnv, m_pCleanupGroup, NULL);

#else
    m_setIdleThreads.LockW();

    g_bInitializedThreadPool = false;
    CMThread* pNewThread = NULL;
    for (cx_dword i = 0U; i < m_dwThreadMinimum; ++i)
    {
        pNewThread = CreateThread(); // ��ʼ��ʱ�������̳߳ػ�δ������ϣ�����ʹ��CREATE_SUSPENDED������߳��������У����ܵ����̳߳ض��γ�ʼ����
        (void)m_setIdleThreads.insert(pNewThread);
    }
    g_bInitializedThreadPool = true;

    m_setIdleThreads.UnlockW();

#endif // WIN32

    return 0;
}

cx_int CMThreadPool::Close()
{
    m_TerminateEvent.Set();

#ifdef WIN32
    //ע�⣺DestroyThreadpoolEnviroment������û��ʲô��
    //Ҫ�������Լ��ͷ��̳߳�
    CloseThreadpoolCleanupGroupMembers(m_pCleanupGroup, FALSE, NULL);
    CloseThreadpoolCleanupGroup(m_pCleanupGroup);
    m_pCleanupGroup = NULL;

    if (m_pThreadPool != NULL) 
    {
        CloseThreadpool(m_pThreadPool);
        m_pThreadPool = NULL;
    }

    //���ٻ����飬�˰汾��DestroyThreadpoolEnviroment��û��ʲô
    //Ҳ����һ���汾���ṩ������ֱ�ӵ��ü���
    DestroyThreadpoolEnvironment(&m_cbEnv);

#else

    DecreateThreads(static_cast<cx_dword>(m_setIdleThreads.size()));

    (void)m_setAliveThreads.LockR();
    std::set<CMThread*> setAliveThreadsCone;
    for (CMThreadSet::iterator itr = m_setAliveThreads.begin(); itr != m_setAliveThreads.end(); ++itr)
    {
        (void)setAliveThreadsCone.insert(*itr);
    }
    (void)m_setAliveThreads.UnlockR();

    for (std::set<CMThread*>::iterator itr = setAliveThreadsCone.begin(); itr != setAliveThreadsCone.end(); ++itr)
    {
        (*itr)->Stop();
    }

    (void)m_setKilledThreads.LockW();
    for (CMThreadSet::iterator itr = m_setKilledThreads.begin(); itr != m_setKilledThreads.end(); ++itr)
    {
        CMThread* pThread = *itr;
        DELETE_S(pThread);
    }
    m_setKilledThreads.clear();
    (void)m_setKilledThreads.UnlockW();

#endif // WIN32

    return 0;
}

cx_bool CMThreadPool::IsTerminate()
{
    return m_TerminateEvent.IsTriggered();
}

cx_int CMThreadPool::SetThreadMinimum(cx_dword dwMin)
{
#ifdef WIN32
    SetThreadpoolThreadMinimum(m_pThreadPool, dwMin);
#endif // WIN32

    return 0;
}

cx_int CMThreadPool::SetThreadMaximum(cx_dword dwMax)
{
#ifdef WIN32
    SetThreadpoolThreadMaximum(m_pThreadPool, dwMax);
#endif // WIN32

    return 0;
}

#ifdef WIN32
VOID NTAPI  CMThreadPool::WorkThread(PTP_CALLBACK_INSTANCE Instance, PVOID pContext)
#else
void*  CMThreadPool::WorkThread(void* const pContext)
#endif
 {
    CMThread*  pThread = NULL;
    if (NULL != pContext)
    {
        pThread = static_cast<CMThread*>(pContext);
    }

    while (1)
    {
        if (!pThread->IsTerminate())
        {
            pThread->Pause();
            ASSERT(pThread->GetWork() != NULL);
            if (pThread->GetWork() != NULL)
            {
                (void)pThread->Start();
            }

            pThread->AssignWork(NULL);
            pThread->FinishWork();
        }

        const cx_bool bTerminate = GetSingleton4ThreadPool()->ThreadFinishWork(pThread);
        if (CMThreadPool::g_bInitializedThreadPool && bTerminate)
        {
            pThread->SetState(CM_TS_TERMINATED);
            break;
        }
        else
        {
            pThread->Pause(); 
        }
    }

#ifdef LINUX
    return NULL;
#endif
}

cx_int CMThreadPool::SubmitThread(CMThread* pThread)
{
    ASSERT(pThread);
    if (NULL == pThread)
    {
        return -1;
    }

#ifdef WIN32
    cx_bool bSuccess = false;
    bSuccess = TrySubmitThreadpoolCallback(pThread->GetThreadProc(), pThread->GetContext(), &m_cbEnv);
#else

#endif // WIN32

    return 0;
}

CMThread * CMThreadPool::CreateThread()
{
//#ifdef LINUX
    CMThread * const pNewThread = new CMThread;

    pNewThread->Create(&WorkThread, static_cast<LPVOID>(pNewThread));
    return pNewThread;

//#else
//    return NULL;
//#endif

}

cx_int CMThreadPool::DecreateThreads(const cx_dword count)
{
    (void)m_setIdleThreads.LockR();
    CMThreadSet::iterator itr = m_setIdleThreads.begin();
    std::set<CMThread*> setIdleThreadClone;
    for (cx_dword i = 0U; i < count; ++i)
    {
        if (itr != m_setIdleThreads.end())
        {
            (void)setIdleThreadClone.insert(*itr);
            ++itr;
        }
    }
    (void)m_setIdleThreads.UnlockR();

    for (CMThreadSet::iterator itrClone = setIdleThreadClone.begin(); itrClone != setIdleThreadClone.end(); ++itrClone)
    {
        (*itrClone)->Stop();
    }

    return 0;
}

cx_bool CMThreadPool::ThreadFinishWork(CMThread * const pThread)
{
    cx_bool bExit = false;
    if (NULL != pThread)
    {
        static cx_bool bThreadPoolExit = false;

        (void)pThread->Reset();

        if (IsTerminate())
        {
            bThreadPoolExit = true;
        }

        bExit = bThreadPoolExit;

        if (!IsTerminate())
        {
            ReleaseThread(pThread);
        }
        else
        {
            bExit = true;
        }

        if (!bExit)
        {
            bExit = pThread->IsTerminate();
        }

        if (bExit)
        {
            (void)m_setIdleThreads.LockW();
            (void)m_setIdleThreads.erase(pThread);
            (void)m_setIdleThreads.UnlockW();

            (void)m_setAliveThreads.LockW();
            (void)m_setAliveThreads.erase(pThread);
            (void)m_setAliveThreads.UnlockW();
            
            (void)m_setKilledThreads.LockW();
            (void)m_setKilledThreads.insert(pThread);
            (void)m_setKilledThreads.UnlockW();
        }
    }
    else
    {
        bExit = true;
    }
    
    return bExit;
}

cx_int CMThreadPool::ReleaseThread(CMThread* const pThread)
{
    ASSERT(pThread);

    if (NULL != pThread)
    {
        (void)m_setAliveThreads.LockW();
        (void)m_setAliveThreads.erase(pThread);
        (void)m_setAliveThreads.UnlockW();
               
        (void)m_setIdleThreads.LockW();
        (void)m_setIdleThreads.insert(pThread);
        (void)m_setIdleThreads.UnlockW();
    }

    return 0;
}

CMThread* CMThreadPool::GetIdleThread()
{
    CMThread* pThread = NULL;

    (void)m_setIdleThreads.LockW();

    if (!m_setIdleThreads.empty()) 
    {
        pThread = *m_setIdleThreads.begin();
        (void)m_setIdleThreads.erase(pThread);
    }
    else
    {
        pThread = CreateThread();
    }

    (void)m_setIdleThreads.UnlockW();

    return pThread;
}

cx_int CMThreadPool::ExecuteTask(CMWork * const pWork, CMThread*& pReturnThread)
{
    CMThread * const pThread = GetIdleThread();
    pReturnThread = pThread;

    if (NULL != pThread)
    {
        (void)m_setAliveThreads.LockW();
        (void)m_setAliveThreads.insert(pThread);
        (void)m_setAliveThreads.UnlockW();

        pThread->AssignWork(pWork);

        pThread->Resume();
    }

    ASSERT(pThread);

    if (pThread)
    {
        SubmitThread(pThread);
    }

    return 0;
}
