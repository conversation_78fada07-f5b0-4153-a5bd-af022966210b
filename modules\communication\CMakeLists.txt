# communication/CMakeLists.txt
cmake_minimum_required(VERSION 2.8.3)
project(communication)

# 全局设置库文件输出目录
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
# set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)

set(CMAKE_CXX_STANDARD 17)
#set(CMAKE_CXX_FLAGS "-std=c++17 -march=native")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall")

if (NOT DEFINED CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE "Debug")
endif()

if(NOT DEFINED PLATFORM)
  set(PLATFORM "x86_64")
endif()

if(NOT DEFINED COMMUNICATION_TYPE)
  set(COMMUNICATION_TYPE "ROS1")
endif()

find_package(PCL REQUIRED)

include_directories(
    ${PCL_INCLUDE_DIRS}
    include
    src
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include
)

if(COMMUNICATION_TYPE STREQUAL "ROS1")
  find_package(catkin REQUIRED COMPONENTS
    roscpp
    rospy
    std_msgs
  )

  include_directories(
    /usr/include/opencv4/
    ${catkin_INCLUDE_DIRS}
    src/ros1
  )
  add_subdirectory(src/ros1)

endif(COMMUNICATION_TYPE STREQUAL "ROS1")  

add_library(${PROJECT_NAME}_core SHARED
  src/communication.cpp
  src/communication_impl.cpp
)

link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../common/lib
              ${CMAKE_CURRENT_SOURCE_DIR}/lib

)
target_link_libraries(${PROJECT_NAME}_core
                    common_lib 
                    ros1_comm                  
)

# add_executable(communication_test
#   src/communication_node.cpp
# )
# target_link_libraries(communication_test
#   ${PROJECT_NAME}_core
#   # ros1_comm
#   # common_lib
# )



