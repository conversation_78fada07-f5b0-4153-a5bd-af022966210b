// modules/communication/src/ros1/communication_ros1_impl.h

#pragma once
#include <ros/ros.h>
#include <memory>
#include "communication_impl.h"

#include "subscriber_base.h"

#include "publisher_base.h"
#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1 {
    // class ImuDataSubscriberRos1; // Forward declaration of the ROS1 IMU data subscriber class
    class CommunicationRos1Impl : public communication::CommunicationImpl
    {
        public:
            CommunicationRos1Impl(const std::string& module_name);
            virtual ~CommunicationRos1Impl();

            //1. subscribers
            //1.1 subscribers for sensor data, such as IMU, GNSS, LiDAR
            virtual std::shared_ptr<ImuDataSubscriberBase> CreateImuDataSubscriber(const std::string& topic) override;
            virtual std::shared_ptr<GnssDataSubscriberBase> CreateGnssDataSubscriber(const std::string& topic) override;
            virtual std::shared_ptr<LidarDataSubscriberBase> CreateLidarDataSubscriber(const std::string& topic) override;
            virtual std::shared_ptr<ImageDataSubscriberBase> CreateImageDataSubscriber(const std::string& topic) override; // camera data subscriber
            //1.2 subscribers for algorithm data
            virtual std::shared_ptr<OdometrySubscriberBase> CreateOdometrySubscriber(const std::string& topic) override;
            virtual std::shared_ptr<IntDataSubscriberBase> CreateIntDataSubscriber(const std::string &topic) override;
            virtual std::shared_ptr<DoubleDataSubscriberBase> CreateDoubleDataSubscriber(const std::string &topic) override;
            virtual std::shared_ptr<StringDataSubscriberBase> CreateStringDataSubscriber(const std::string &topic) override;
            virtual std::shared_ptr<BoolDataSubscriberBase> CreateBoolDataSubscriber(const std::string &topic) override;
            virtual std::shared_ptr<CloudDataSubscriberBase> CreateCloudDataSubscriber(const std::string &topic) override;
            virtual std::shared_ptr<PathDataSubscriberBase> CreatePathDataSubscriber(const std::string &topic) override;
            virtual std::shared_ptr<PoseDataSubscriberBase> CreatePoseDataSubscriber(const std::string &topic) override;
            virtual std::shared_ptr<CameraIntSubscriberBase> CreateCameraIntSubscriber(const std::string &topic) override; // Camera Intrinsics data subscriber
            virtual std::shared_ptr<CameraExtSubscriberBase> CreateCameraExtSubscriber(const std::string &topic) override; // camera Extrinsics data subscriber

            // 2. publishers for algorithm data
            // 2.1 Create publishers for simple data types (e.g., int, float, double)
            virtual std::shared_ptr<OdometryPublisherBase> CreateOdometryPublisher(const std::string& topic, 
                                                        const std::string& frame_id = "map", const std::string& child_frame_id = "body", size_t max_buffer_size = 10) override;
            virtual std::shared_ptr<IntDataPublisherBase> CreateIntDataPublisher(const std::string &topic, size_t max_buffer_size = 10) override;
            virtual std::shared_ptr<DoubleDataPublisherBase> CreateDoubleDataPublisher(const std::string &topic, size_t max_buffer_size = 10) override;
            virtual std::shared_ptr<StringDataPublisherBase> CreateStringDataPublisher(const std::string &topic, size_t max_buffer_size = 10) override;
            virtual std::shared_ptr<BoolDataPublisherBase> CreateBoolDataPublisher(const std::string &topic, size_t max_buffer_size = 10) override;
            virtual std::shared_ptr<CloudDataPublisherBase> CreateCloudDataPublisher(const std::string &topic, 
                                                        const std::string& frame_id = "map", size_t max_buffer_size = 10) override;
            virtual std::shared_ptr<PathDataPublisherBase> CreatePathDataPublisher(const std::string &topic,
                                                        const std::string& frame_id = "map", size_t max_buffer_size = 10) override;

            virtual std::shared_ptr<PoseDataPublisherBase> CreatePoseDataPublisher(const std::string &topic, size_t max_buffer_size = 10) override;
            protected:
            // Add protected members if needed for ROS1 communication
            virtual bool Initialize(const CommunicationType type) override;

            virtual void Run() override;

        private:
            ros::NodeHandle nh_; // NodeHandle for ROS1 communication

    };

} // namespace communication {

#endif // COMMUNICATION_TYPE == ROS1