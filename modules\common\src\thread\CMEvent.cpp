#include "CMEvent.h"

#ifdef WIN32
CMEvent::CMEvent(cx_bool bIsManualReset, cx_bool bInitialSignaled)
    : m_bIsManualReset(bIsManualReset)
    , m_bEventStatus(bInitialSignaled)
{
}

CMEvent::~CMEvent()
{
    CloseHandle(m_hEvent);
}

cx_bool CMEvent::Create()
{
    m_hEvent = CreateEvent(NULL, m_bIsManualReset, m_bEventStatus, NULL);

    if (m_hEvent != NULL)
    {
       return true;
    }
    else
    {
        return false;
    }
}

cx_bool CMEvent::Set()
{
    return SetEvent(m_hEvent);
}

cx_bool CMEvent::Reset()
{
    return ResetEvent(m_hEvent);
}

cx_bool CMEvent::Wait(int cms)
{
    cx_int iResult = WaitForSingleObject(m_hEvent, cms);
    return (WAIT_OBJECT_0 == iResult);
}

cx_bool CMEvent::EnsureInitialized()
{
    return (m_hEvent != NULL);
}

cx_bool CMEvent::IsTriggered()
{
    return Wait(0);
}

cx_bool CMEvent::Destroy()
{
    CloseHandle(m_hEvent);
    m_hEvent = NULL;

    return 0;
}

#else

CMEvent::CMEvent(cx_bool bIsManualReset, cx_bool bInitialSignaled)
    : m_bIsManualReset(bIsManualReset)
    , m_bEventStatus(bInitialSignaled)
    , m_bMutexInitialized(false)
    , m_bCondInitialized(false)
{
}

CMEvent::~CMEvent()
{
    if (m_bMutexInitialized)
    {
        pthread_mutex_destroy(&m_mutex);
        m_bMutexInitialized = false;
    }
       
    if (m_bCondInitialized)
    {
        pthread_cond_destroy(&m_cond);
        m_bCondInitialized = false;
    }
}

cx_bool CMEvent::Create()
{
    if (!m_bMutexInitialized)
    {
        if (0 == pthread_mutex_init(&m_mutex, NULL))
        {
            m_bMutexInitialized = true;
        }
    }

    if (!m_bCondInitialized)
    {
        if (0 == pthread_cond_init(&m_cond, NULL))
        {
            m_bCondInitialized = true;
        }
    }

    return (m_bMutexInitialized && m_bCondInitialized);
}

cx_bool CMEvent::EnsureInitialized()
{
    return (m_bMutexInitialized && m_bCondInitialized);
}

cx_bool CMEvent::Set()
{
    if (!EnsureInitialized())
    {
        return false;
    }   

    pthread_mutex_lock(&m_mutex);
    m_bEventStatus = true;
    pthread_cond_broadcast(&m_cond);
    pthread_mutex_unlock(&m_mutex);

    return true;
}

cx_bool CMEvent::Reset()
{
    if (!EnsureInitialized())
    {
        return false;
    }

    pthread_mutex_lock(&m_mutex);
    m_bEventStatus = false;
    pthread_mutex_unlock(&m_mutex);

    return true;
}

cx_bool CMEvent::Wait(cx_int cms)
{
    if (!EnsureInitialized())
    {
        return false;
    }

    pthread_mutex_lock(&m_mutex);
    cx_int error = 0;
    if (cms != INFINITE)
    {
        struct timeval tv;
        gettimeofday(&tv, NULL);

        struct timespec ts;
        ts.tv_sec = tv.tv_sec + (cms / 1000);
        ts.tv_nsec = tv.tv_usec * 1000 + (cms % 1000) * 1000000;

        if (ts.tv_nsec >= 1000000000)
        {
            ts.tv_sec++;
            ts.tv_nsec -= 1000000000;
        }
               
        while ((!m_bEventStatus) && (error == 0))
        {
            error = pthread_cond_timedwait(&m_cond, &m_mutex, &ts);
        }
    }
    else
    {
        while ((!m_bEventStatus) && (error == 0))
        {
            error = pthread_cond_wait(&m_cond, &m_mutex);
        }
    }
       
    if (0 == error && !m_bIsManualReset)
    {
        m_bEventStatus = false;
    }

    pthread_mutex_unlock(&m_mutex);

    return (0 == error);
}

cx_bool CMEvent::IsTriggered()
{
    return m_bEventStatus;
}

cx_bool CMEvent::Destroy()
{
    if (m_bMutexInitialized)
    {
        pthread_mutex_destroy(&m_mutex);
        m_bMutexInitialized = false;
    }

    if (m_bCondInitialized)
    {
        pthread_cond_destroy(&m_cond);
        m_bCondInitialized = false;
    }

    return true;
}

#endif
