﻿#ifndef _CM_MUTEX_H_
#define _CM_MUTEX_H_

#include "CM_pubhead.h"

#ifdef WIN32
#include <windows.h>
#else
#include <pthread.h>
#endif

class CMMutex
{
public:
    CMMutex();
    virtual ~CMMutex();

public:
    cx_int Lock();
    cx_int Unlock();

    cx_int SetRecursiveAttr();

protected:

#ifdef WIN32
    CRITICAL_SECTION m_cs;
#else
    pthread_mutex_t m_mutex;
#endif

    friend class CMAutoMutex;
};


#endif  // _CM_MUTEX_H_
