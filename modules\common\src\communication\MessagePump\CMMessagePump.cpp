#include "CMMessagePump.h"
#include "CMSingleton.h"
#include "CMTemplate.h"

CMMessagePump* GetSingleton4MessagePump()
{
    static CMSingleton<CMMessagePump>   s_MessagePump;

    return s_MessagePump.GetSingletonInstance();
}

CMMessagePump::CMMessagePump()
{

}

CMMessagePump::~CMMessagePump()
{
    m_deqMessages.LockW();

    FreeDequeContainer(m_deqMessages.m_data);

    m_deqMessages.UnlockW();
}

cx_int CMMessagePump::SendMessage(const CMMessage& msg)
{
    ASSERT(0);

    return 0;
}

cx_int CMMessagePump::PostMessage(const CMMessage& msg)
{
    m_deqMessages.LockW();

    CMMessage* pNewMessage = new CMMessage();
    *pNewMessage = msg;
    m_deqMessages.m_data.push_back(pNewMessage);

    m_deqMessages.UnlockW();

    return 0;
}

cx_int CMMessagePump::GetMessage(CMMessage& msg)
{
    cx_int iResult = 0;

    m_deqMessages.LockW();

    if (!m_deqMessages.m_data.empty())
    {
        CMMessage* pMessage = m_deqMessages.m_data.front();
        ASSERT(pMessage);
        msg = *pMessage;
        m_deqMessages.m_data.pop_front();
        DELETE_S(pMessage);
    }
    else
    {
        iResult = -1;
    }

    m_deqMessages.UnlockW();

    return iResult;
}

cx_int CMMessagePump::GetBackMessage(CMMessage& msg)
{
    cx_int iResult = 0;

    m_deqMessages.LockW();

    if (!m_deqMessages.m_data.empty())
    {
        CMMessage* pMessage = m_deqMessages.m_data.back();
        ASSERT(pMessage);
        msg = *pMessage;
    }
    else
    {
        iResult = -1;
    }

    m_deqMessages.UnlockW();

    return iResult;
}

cx_int CMMessagePump::PeekMessage(CMMessage& msg)
{
    ASSERT(0);

    return 0;
}

cx_int CMMessagePump::DestroyMessage(CMMessage& msg)
{
    switch (msg.appId)
    {
        case MSG_APP_UNKNOW:
        {
            break;
        }
        case MSG_APP_CAN:
        {
            break;
        }
        case MSG_APP_ZMQ:
        {               
            break;
        }
        case MSG_APP_SERIAL:
        {          
            break;
        }
        default:
        {
            ASSERT(0);
            DELETE_S(msg.lParam);
            break;
        }
    }

    return 0;
}

cx_int PostMessage(const CMMessage& msg)
{
    GetSingleton4MessagePump()->PostMessage(msg);
}

cx_int GetMessage(CMMessage& msg)
{
    GetSingleton4MessagePump()->GetMessage(msg);
}
