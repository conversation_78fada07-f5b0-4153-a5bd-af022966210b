#include "rrt_star_global_planner.h"
#include <algorithm>
#include <limits>

using namespace RRTstar_planner;

// 构造函数实现
RRTstarPlannerNoRos::RRTstarPlannerNoRos() 
    : costmap_(nullptr), frame_id_("map"), max_nodes_num_(2000000), plan_time_out_(10.0),
      search_radius_(1.0), goal_radius_(0.2), epsilon_min_(0.001), epsilon_max_(0.1),
      path_point_spacing_(0.25), angle_difference_(M_PI/20), resolution_(0.05), 
      initialized_(false), rng_(std::random_device{}()), uniform_dist_(0.0, 1.0) {
    
    // 初始化可视化标记
    marker_tree_.type = Marker::LINE_LIST;
    marker_tree_.action = Marker::ADD;
    marker_tree_.scale = Marker::Scale(0.02, 0.02, 0.02);
    marker_tree_.color = Marker::Color(1.0, 0.0, 0.0, 1.0); // 红色
    marker_tree_.header.frame_id = frame_id_;
    
    marker_tree_2_.type = Marker::LINE_LIST;
    marker_tree_2_.action = Marker::ADD;
    marker_tree_2_.scale = Marker::Scale(0.02, 0.02, 0.02);
    marker_tree_2_.color = Marker::Color(0.0, 1.0, 0.0, 1.0); // 绿色
    marker_tree_2_.header.frame_id = frame_id_;
}

RRTstarPlannerNoRos::RRTstarPlannerNoRos(std::string name, Costmap2D* costmap)
    : RRTstarPlannerNoRos() {
    initialize(name, costmap);
}

void RRTstarPlannerNoRos::initialize(std::string name, Costmap2D* costmap) {
    if (initialized_) {
        std::cout << "RRTstarPlannerNoRos 已经初始化" << std::endl;
        return;
    }
    
    costmap_ = costmap;
    if (costmap_) {
        resolution_ = costmap_->resolution;
        frame_id_ = "map"; // 默认坐标系
    }
    
    initialized_ = true;
    std::cout << "RRTstarPlannerNoRos 初始化完成: " << name << std::endl;
    std::cout << "  分辨率: " << resolution_ << " m/pixel" << std::endl;
    std::cout << "  坐标系: " << frame_id_ << std::endl;
    std::cout << "  最大节点数: " << max_nodes_num_ << std::endl;
    std::cout << "  规划超时: " << plan_time_out_ << " 秒" << std::endl;
}

bool RRTstarPlannerNoRos::makePlan(const PoseStamped& start,
                                   const PoseStamped& goal,
                                   std::vector<PoseStamped>& plan) {
    plan.clear();
    
    if (!initialized_) {
        std::cerr << "RRTstarPlannerNoRos 未初始化" << std::endl;
        return false;
    }
    
    if (!costmap_) {
        std::cerr << "代价地图未设置" << std::endl;
        return false;
    }
    
    std::cout << "开始RRT*路径规划..." << std::endl;
    std::cout << "起点: (" << start.pose.position.x << ", " << start.pose.position.y << ")" << std::endl;
    std::cout << "目标: (" << goal.pose.position.x << ", " << goal.pose.position.y << ")" << std::endl;
    
    // 检查起点和终点是否在障碍物上
    if (collision(start.pose.position.x, start.pose.position.y)) {
        std::cerr << "起点位于障碍物上" << std::endl;
        if (accessable_callback_) {
            accessable_callback_(false);
        }
        return false;
    }
    
    if (collision(goal.pose.position.x, goal.pose.position.y)) {
        std::cerr << "目标点位于障碍物上" << std::endl;
        if (accessable_callback_) {
            accessable_callback_(false);
        }
        return false;
    }
    
    // 双向RRT*算法实现
    std::vector<Node> nodes;     // 第一棵树 (从起点)
    std::vector<Node> nodes_2;   // 第二棵树 (从目标)
    
    nodes.reserve(max_nodes_num_ / 2);
    nodes_2.reserve(max_nodes_num_ / 2);
    
    // 初始化两棵树
    Node start_node(start.pose.position.x, start.pose.position.y, 0, -1, 0.0);
    Node goal_node(goal.pose.position.x, goal.pose.position.y, 0, -1, 0.0);
    nodes.push_back(start_node);
    nodes_2.push_back(goal_node);
    
    Node connect_node_on_tree1, connect_node_on_tree2;
    bool is_connect_to_tree2 = false, is_connect_to_tree1 = false;
    bool goal_reached_from_tree1 = false, start_reached_from_tree2 = false;
    
    auto start_time = std::chrono::steady_clock::now();
    
    while (nodes.size() + nodes_2.size() < max_nodes_num_) {
        // 检查超时
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time);
        if (elapsed.count() > plan_time_out_) {
            std::cout << "规划超时" << std::endl;
            break;
        }
        
        // 第一棵树扩展
        std::pair<double, double> p_rand = sampleFree();
        Node node_nearest = getNearest(nodes, p_rand);
        std::pair<double, double> p_new = steer(node_nearest.x, node_nearest.y, p_rand.first, p_rand.second);
        
        if (obstacleFree(node_nearest, p_new.first, p_new.second)) {
            Node newnode(p_new.first, p_new.second, nodes.size(), node_nearest.node_id,
                        node_nearest.cost + distance(node_nearest.x, node_nearest.y, p_new.first, p_new.second));
            
            newnode = chooseParent(node_nearest, newnode, nodes);
            nodes.push_back(newnode);
            rewire(nodes, newnode);
            
            // 检查与第二棵树的连接
            if (isConnect(newnode, nodes_2, nodes, connect_node_on_tree2)) {
                is_connect_to_tree2 = true;
                connect_node_on_tree1 = newnode;
                std::cout << "两棵树连接在了一起！" << std::endl;
                break;
            }
            
            // 检查是否到达目标
            if (pointCircleCollision(newnode.x, newnode.y, goal.pose.position.x, goal.pose.position.y, goal_radius_)) {
                goal_reached_from_tree1 = true;
                connect_node_on_tree1 = newnode;
                std::cout << "第一棵树到达目标！" << std::endl;
                break;
            }
        }
        
        // 第二棵树扩展
        if (!nodes.empty()) {
            p_rand = std::make_pair(nodes.back().x, nodes.back().y); // 使用第一棵树的新节点作为目标
        } else {
            p_rand = sampleFree();
        }
        
        node_nearest = getNearest(nodes_2, p_rand);
        p_new = steer(node_nearest.x, node_nearest.y, p_rand.first, p_rand.second);
        
        if (obstacleFree(node_nearest, p_new.first, p_new.second)) {
            Node newnode_2(p_new.first, p_new.second, nodes_2.size(), node_nearest.node_id,
                          node_nearest.cost + distance(node_nearest.x, node_nearest.y, p_new.first, p_new.second));
            
            newnode_2 = chooseParent(node_nearest, newnode_2, nodes_2);
            nodes_2.push_back(newnode_2);
            rewire(nodes_2, newnode_2);
            
            // 检查与第一棵树的连接
            if (isConnect(newnode_2, nodes, nodes_2, connect_node_on_tree1)) {
                is_connect_to_tree1 = true;
                connect_node_on_tree2 = newnode_2;
                std::cout << "两棵树连接在了一起！" << std::endl;
                break;
            }
            
            // 检查是否到达起点
            if (pointCircleCollision(newnode_2.x, newnode_2.y, start.pose.position.x, start.pose.position.y, goal_radius_)) {
                start_reached_from_tree2 = true;
                connect_node_on_tree2 = newnode_2;
                std::cout << "第二棵树到达起点！" << std::endl;
                break;
            }
        }
        
        // 平衡树的大小
        if (nodes.size() > nodes_2.size() + 100) {
            std::swap(nodes, nodes_2);
            std::swap(is_connect_to_tree1, is_connect_to_tree2);
            std::swap(connect_node_on_tree1, connect_node_on_tree2);
        }
        
        // 更新可视化 (每100个节点更新一次)
        if ((nodes.size() + nodes_2.size()) % 100 == 0) {
            // 更新第一棵树的可视化
            marker_tree_.clear();
            marker_tree_.id = 0;
            for (const auto& node : nodes) {
                if (node.parent_id != -1) {
                    // 找到父节点
                    for (const auto& parent : nodes) {
                        if (parent.node_id == node.parent_id) {
                            marker_tree_.addPoint(parent.x, parent.y, 0.0);
                            marker_tree_.addPoint(node.x, node.y, 0.0);
                            break;
                        }
                    }
                }
            }
            
            // 更新第二棵树的可视化
            marker_tree_2_.clear();
            marker_tree_2_.id = 1;
            for (const auto& node : nodes_2) {
                if (node.parent_id != -1) {
                    // 找到父节点
                    for (const auto& parent : nodes_2) {
                        if (parent.node_id == node.parent_id) {
                            marker_tree_2_.addPoint(parent.x, parent.y, 0.0);
                            marker_tree_2_.addPoint(node.x, node.y, 0.0);
                            break;
                        }
                    }
                }
            }
            
            // 发布可视化
            if (marker_callback_) {
                marker_callback_(marker_tree_);
                marker_callback_(marker_tree_2_);
            }
        }
    }
    
    std::cout << "搜索完成，第一棵树节点数: " << nodes.size() 
              << ", 第二棵树节点数: " << nodes_2.size() << std::endl;
    
    // 确定路径提取模式
    GetPlanMode mode;
    if (is_connect_to_tree2) {
        mode = CONNECT1TO2;
        std::cout << "使用连接模式: CONNECT1TO2" << std::endl;
    } else if (is_connect_to_tree1) {
        mode = CONNECT2TO1;
        std::cout << "使用连接模式: CONNECT2TO1" << std::endl;
    } else if (goal_reached_from_tree1) {
        mode = TREE1;
        std::cout << "使用第一棵树路径" << std::endl;
    } else if (start_reached_from_tree2) {
        mode = TREE2;
        std::cout << "使用第二棵树路径" << std::endl;
    } else {
        std::cout << "未找到可行路径" << std::endl;
        if (accessable_callback_) {
            accessable_callback_(false);
        }
        return false;
    }
    
    // 提取路径
    getPathFromTree(nodes, nodes_2, connect_node_on_tree1, plan, mode);
    
    if (plan.empty()) {
        std::cout << "路径提取失败" << std::endl;
        if (accessable_callback_) {
            accessable_callback_(false);
        }
        return false;
    }
    
    std::cout << "路径规划成功，路径点数: " << plan.size() << std::endl;
    
    // 发布路径
    if (path_callback_) {
        path_callback_(plan);
    }
    
    if (accessable_callback_) {
        accessable_callback_(true);
    }
    
    return true;
}

// 路径提取函数实现
void RRTstarPlannerNoRos::getPathFromTree1ConnectTree2(std::vector<Node>& tree1,
                                                       std::vector<Node>& tree2,
                                                       Node& connect_node,
                                                       std::vector<PoseStamped>& plan) {
    getPathFromTree(tree1, tree2, connect_node, plan, CONNECT1TO2);
}

void RRTstarPlannerNoRos::getPathFromTree(std::vector<Node>& tree1,
                                          std::vector<Node>& tree2,
                                          Node& connect_node,
                                          std::vector<PoseStamped>& plan,
                                          GetPlanMode mode) {
    plan.clear();
    std::vector<std::pair<double, double>> path_points;

    switch (mode) {
        case TREE1: {
            // 从第一棵树提取路径
            Node current = connect_node;
            while (current.parent_id != -1) {
                path_points.emplace_back(current.x, current.y);
                // 找到父节点
                for (const auto& node : tree1) {
                    if (node.node_id == current.parent_id) {
                        current = node;
                        break;
                    }
                }
            }
            path_points.emplace_back(tree1[0].x, tree1[0].y); // 起点
            std::reverse(path_points.begin(), path_points.end());
            break;
        }

        case TREE2: {
            // 从第二棵树提取路径
            Node current = connect_node;
            while (current.parent_id != -1) {
                path_points.emplace_back(current.x, current.y);
                // 找到父节点
                for (const auto& node : tree2) {
                    if (node.node_id == current.parent_id) {
                        current = node;
                        break;
                    }
                }
            }
            path_points.emplace_back(tree2[0].x, tree2[0].y); // 目标点
            // 不需要反转，因为是从目标到起点
            break;
        }

        case CONNECT1TO2: {
            // 连接两棵树：tree1 -> tree2
            // 从tree1的连接点回溯到起点
            Node current = connect_node;
            std::vector<std::pair<double, double>> path1;
            while (current.parent_id != -1) {
                path1.emplace_back(current.x, current.y);
                for (const auto& node : tree1) {
                    if (node.node_id == current.parent_id) {
                        current = node;
                        break;
                    }
                }
            }
            path1.emplace_back(tree1[0].x, tree1[0].y); // 起点
            std::reverse(path1.begin(), path1.end());

            // 从tree2的连接点回溯到目标
            // 找到tree2中最近的连接节点
            Node connect_in_tree2;
            double min_dist = std::numeric_limits<double>::infinity();
            for (const auto& node : tree2) {
                double dist = distance(node.x, node.y, connect_node.x, connect_node.y);
                if (dist < min_dist) {
                    min_dist = dist;
                    connect_in_tree2 = node;
                }
            }

            std::vector<std::pair<double, double>> path2;
            current = connect_in_tree2;
            while (current.parent_id != -1) {
                path2.emplace_back(current.x, current.y);
                for (const auto& node : tree2) {
                    if (node.node_id == current.parent_id) {
                        current = node;
                        break;
                    }
                }
            }
            path2.emplace_back(tree2[0].x, tree2[0].y); // 目标点

            // 合并路径
            path_points = path1;
            path_points.insert(path_points.end(), path2.begin(), path2.end());
            break;
        }

        case CONNECT2TO1: {
            // 连接两棵树：tree2 -> tree1
            // 类似CONNECT1TO2，但顺序相反
            Node current = connect_node;
            std::vector<std::pair<double, double>> path2;
            while (current.parent_id != -1) {
                path2.emplace_back(current.x, current.y);
                for (const auto& node : tree2) {
                    if (node.node_id == current.parent_id) {
                        current = node;
                        break;
                    }
                }
            }
            path2.emplace_back(tree2[0].x, tree2[0].y); // 目标点

            // 找到tree1中最近的连接节点
            Node connect_in_tree1;
            double min_dist = std::numeric_limits<double>::infinity();
            for (const auto& node : tree1) {
                double dist = distance(node.x, node.y, connect_node.x, connect_node.y);
                if (dist < min_dist) {
                    min_dist = dist;
                    connect_in_tree1 = node;
                }
            }

            std::vector<std::pair<double, double>> path1;
            current = connect_in_tree1;
            while (current.parent_id != -1) {
                path1.emplace_back(current.x, current.y);
                for (const auto& node : tree1) {
                    if (node.node_id == current.parent_id) {
                        current = node;
                        break;
                    }
                }
            }
            path1.emplace_back(tree1[0].x, tree1[0].y); // 起点
            std::reverse(path1.begin(), path1.end());

            // 合并路径
            path_points = path1;
            path_points.insert(path_points.end(), path2.begin(), path2.end());
            break;
        }
    }

    // 路径优化
    cutPathPoint(path_points);
    insertPointForPath(path_points, path_point_spacing_);
    optimizationPath(path_points);

    // 转换为PoseStamped格式
    plan.resize(path_points.size());
    for (size_t i = 0; i < path_points.size(); ++i) {
        plan[i].header.frame_id = frame_id_;
        plan[i].header.stamp = PoseStamped::getCurrentTime();
        plan[i].pose.position.x = path_points[i].first;
        plan[i].pose.position.y = path_points[i].second;
        plan[i].pose.position.z = 0.0;

        // 计算朝向
        if (i < path_points.size() - 1) {
            double dx = path_points[i + 1].first - path_points[i].first;
            double dy = path_points[i + 1].second - path_points[i].second;
            double yaw = atan2(dy, dx);
            plan[i].pose.orientation.x = 0.0;
            plan[i].pose.orientation.y = 0.0;
            plan[i].pose.orientation.z = sin(yaw / 2.0);
            plan[i].pose.orientation.w = cos(yaw / 2.0);
        } else if (i > 0) {
            // 最后一个点使用前一个点的朝向
            plan[i].pose.orientation = plan[i-1].pose.orientation;
        }
    }

    // 优化朝向
    optimizationOrientation(plan);
}

// 距离计算函数
double RRTstarPlannerNoRos::distance(double px1, double py1, double px2, double py2) {
    return sqrt((px1 - px2) * (px1 - px2) + (py1 - py2) * (py1 - py2));
}

// 随机采样函数
std::pair<double, double> RRTstarPlannerNoRos::sampleFree() {
    if (!costmap_) {
        return std::make_pair(0.0, 0.0);
    }

    double x, y;
    int attempts = 0;
    const int max_attempts = 1000;

    do {
        x = costmap_->origin_x + uniform_dist_(rng_) * costmap_->width * costmap_->resolution;
        y = costmap_->origin_y + uniform_dist_(rng_) * costmap_->height * costmap_->resolution;
        attempts++;
    } while (collision(x, y) && attempts < max_attempts);

    if (attempts >= max_attempts) {
        // 如果无法找到自由空间，返回地图中心
        x = costmap_->origin_x + costmap_->width * costmap_->resolution * 0.5;
        y = costmap_->origin_y + costmap_->height * costmap_->resolution * 0.5;
    }

    return std::make_pair(x, y);
}

// 碰撞检测函数
bool RRTstarPlannerNoRos::collision(double x, double y) {
    if (!costmap_) return true;

    int mx, my;
    if (!costmap_->worldToMap(x, y, mx, my)) {
        return true; // 超出地图边界视为碰撞
    }

    return costmap_->getCost(mx, my) > 50; // 阈值可配置
}

// 周围自由检测函数
bool RRTstarPlannerNoRos::isAroundFree(double wx, double wy) {
    if (!costmap_) return false;

    // 检查周围一定范围内是否自由
    double check_radius = 0.1; // 检查半径
    int steps = 8;

    for (int i = 0; i < steps; ++i) {
        double angle = 2.0 * M_PI * i / steps;
        double check_x = wx + check_radius * cos(angle);
        double check_y = wy + check_radius * sin(angle);

        if (collision(check_x, check_y)) {
            return false;
        }
    }

    return true;
}

// 树连接检测函数
bool RRTstarPlannerNoRos::isConnect(Node new_node, std::vector<Node>& another_tree,
                                   std::vector<Node>& current_tree, Node& connect_node) {
    for (const auto& node : another_tree) {
        double dist = distance(new_node.x, new_node.y, node.x, node.y);
        if (dist < goal_radius_) {
            if (isLineFree(std::make_pair(new_node.x, new_node.y), std::make_pair(node.x, node.y))) {
                connect_node = node;
                return true;
            }
        }
    }
    return false;
}

// 最近邻搜索函数
Node RRTstarPlannerNoRos::getNearest(std::vector<Node> nodes, std::pair<double, double> p_rand) {
    double min_dist = std::numeric_limits<double>::infinity();
    Node nearest;

    for (const auto& node : nodes) {
        double dist = distance(node.x, node.y, p_rand.first, p_rand.second);
        if (dist < min_dist) {
            min_dist = dist;
            nearest = node;
        }
    }

    return nearest;
}

// 父节点选择函数
Node RRTstarPlannerNoRos::chooseParent(Node nn, Node newnode, std::vector<Node> nodes) {
    // 在搜索半径内寻找更好的父节点
    for (const auto& node : nodes) {
        double dist = distance(node.x, node.y, newnode.x, newnode.y);
        if (dist < search_radius_) {
            double new_cost = node.cost + dist;
            if (new_cost < newnode.cost &&
                isLineFree(std::make_pair(node.x, node.y), std::make_pair(newnode.x, newnode.y)) &&
                isAroundFree(newnode.x, newnode.y)) {
                newnode.cost = new_cost;
                newnode.parent_id = node.node_id;
            }
        }
    }

    return newnode;
}

// 重连函数
void RRTstarPlannerNoRos::rewire(std::vector<Node>& nodes, Node newnode) {
    // 在搜索半径内重连节点
    for (auto& node : nodes) {
        if (node.node_id == newnode.node_id) continue;

        double dist = distance(node.x, node.y, newnode.x, newnode.y);
        if (dist < search_radius_) {
            double new_cost = newnode.cost + dist;
            if (new_cost < node.cost &&
                isLineFree(std::make_pair(newnode.x, newnode.y), std::make_pair(node.x, node.y)) &&
                isAroundFree(node.x, node.y)) {
                node.cost = new_cost;
                node.parent_id = newnode.node_id;
            }
        }
    }
}

// 扩展函数
std::pair<double, double> RRTstarPlannerNoRos::steer(double x1, double y1, double x2, double y2) {
    double dist = distance(x1, y1, x2, y2);

    if (dist <= epsilon_max_) {
        return std::make_pair(x2, y2);
    }

    double theta = atan2(y2 - y1, x2 - x1);
    double new_x = x1 + epsilon_max_ * cos(theta);
    double new_y = y1 + epsilon_max_ * sin(theta);

    return std::make_pair(new_x, new_y);
}

// 障碍物自由检测函数
bool RRTstarPlannerNoRos::obstacleFree(Node node_nearest, double px, double py) {
    return isLineFree(std::make_pair(node_nearest.x, node_nearest.y), std::make_pair(px, py));
}

// 点圆碰撞检测函数
bool RRTstarPlannerNoRos::pointCircleCollision(double x1, double y1, double x2, double y2, double radius) {
    return distance(x1, y1, x2, y2) <= radius;
}

// 朝向优化函数
void RRTstarPlannerNoRos::optimizationOrientation(std::vector<PoseStamped>& plan) {
    if (plan.size() < 2) return;

    for (size_t i = 0; i < plan.size() - 1; ++i) {
        double dx = plan[i + 1].pose.position.x - plan[i].pose.position.x;
        double dy = plan[i + 1].pose.position.y - plan[i].pose.position.y;
        double yaw = atan2(dy, dx);

        plan[i].pose.orientation.x = 0.0;
        plan[i].pose.orientation.y = 0.0;
        plan[i].pose.orientation.z = sin(yaw / 2.0);
        plan[i].pose.orientation.w = cos(yaw / 2.0);
    }

    // 最后一个点保持前一个点的朝向
    if (plan.size() > 1) {
        plan.back().pose.orientation = plan[plan.size() - 2].pose.orientation;
    }
}

// 路径插点函数
void RRTstarPlannerNoRos::insertPointForPath(std::vector<std::pair<double, double>>& pathin, double param) {
    if (pathin.size() < 2) return;

    std::vector<std::pair<double, double>> new_path;
    new_path.push_back(pathin[0]);

    for (size_t i = 1; i < pathin.size(); ++i) {
        double dx = pathin[i].first - pathin[i-1].first;
        double dy = pathin[i].second - pathin[i-1].second;
        double dist = sqrt(dx * dx + dy * dy);

        if (dist > param) {
            int num_points = static_cast<int>(dist / param);
            double step_x = dx / (num_points + 1);
            double step_y = dy / (num_points + 1);

            for (int j = 1; j <= num_points; ++j) {
                double new_x = pathin[i-1].first + j * step_x;
                double new_y = pathin[i-1].second + j * step_y;
                new_path.emplace_back(new_x, new_y);
            }
        }

        new_path.push_back(pathin[i]);
    }

    pathin = new_path;
}

// 路径优化函数
int RRTstarPlannerNoRos::optimizationPath(std::vector<std::pair<double, double>>& plan, double movement_angle_range) {
    if (plan.size() < 3) return 0;

    int removed_points = 0;
    bool changed = true;

    while (changed) {
        changed = false;

        for (size_t i = 1; i < plan.size() - 1; ++i) {
            // 检查是否可以直接连接前一个点和后一个点
            if (isLineFree(plan[i-1], plan[i+1])) {
                // 检查角度变化
                double angle1 = atan2(plan[i].second - plan[i-1].second,
                                    plan[i].first - plan[i-1].first);
                double angle2 = atan2(plan[i+1].second - plan[i].second,
                                    plan[i+1].first - plan[i].first);
                double angle_diff = fabs(normalizeAngle(angle2 - angle1));

                if (angle_diff < movement_angle_range) {
                    plan.erase(plan.begin() + i);
                    removed_points++;
                    changed = true;
                    break;
                }
            }
        }
    }

    return removed_points;
}

// 线段自由检测函数
bool RRTstarPlannerNoRos::isLineFree(const std::pair<double, double> p1, const std::pair<double, double> p2) {
    if (!costmap_) return false;

    double dx = p2.first - p1.first;
    double dy = p2.second - p1.second;
    double dist = sqrt(dx * dx + dy * dy);

    if (dist < epsilon_min_) {
        return !collision(p2.first, p2.second);
    }

    int steps = static_cast<int>(dist / resolution_) + 1;
    double step_x = dx / steps;
    double step_y = dy / steps;

    for (int i = 0; i <= steps; ++i) {
        double x = p1.first + i * step_x;
        double y = p1.second + i * step_y;

        if (collision(x, y)) {
            return false;
        }
    }

    return true;
}

// 路径点裁剪函数
void RRTstarPlannerNoRos::cutPathPoint(std::vector<std::pair<double, double>>& plan) {
    if (plan.size() < 3) return;

    std::vector<std::pair<double, double>> optimized_path;
    optimized_path.push_back(plan[0]);

    for (size_t i = 1; i < plan.size() - 1; ++i) {
        // 检查当前点是否必要
        bool necessary = true;

        if (optimized_path.size() > 0) {
            const auto& last_point = optimized_path.back();
            const auto& next_point = plan[i + 1];

            // 如果可以直接从上一个保留的点到下一个点，则当前点可能是冗余的
            if (isLineFree(last_point, next_point)) {
                necessary = false;
            }
        }

        if (necessary) {
            optimized_path.push_back(plan[i]);
        }
    }

    optimized_path.push_back(plan.back());
    plan = optimized_path;
}

// 角度标准化函数
double RRTstarPlannerNoRos::normalizeAngle(double val, double min, double max) {
    double width = max - min;
    double offsetValue = val - min;

    return (offsetValue - (floor(offsetValue / width) * width)) + min;
}

// 可视化发布函数
void RRTstarPlannerNoRos::pubTreeMarker(Publisher<Marker>& marker_pub, Marker marker, int id) {
    marker.id = id;
    marker.header.stamp = PoseStamped::getCurrentTime();
    marker_pub.publish(marker);
}

// 新增的去ROS化接口实现
void RRTstarPlannerNoRos::setMapFromFile(const std::string& map_file) {
    if (!costmap_) {
        costmap_ = new Costmap2D();
    }

    if (costmap_->loadFromFile(map_file)) {
        resolution_ = costmap_->resolution;
        std::cout << "从文件加载地图成功: " << map_file << std::endl;
        std::cout << "地图尺寸: " << costmap_->width << "x" << costmap_->height << std::endl;
        std::cout << "分辨率: " << resolution_ << " m/pixel" << std::endl;
    } else {
        std::cerr << "从文件加载地图失败: " << map_file << std::endl;
    }
}

void RRTstarPlannerNoRos::setMapFromData(const std::vector<std::vector<unsigned char>>& map_data,
                                        double resolution, double origin_x, double origin_y) {
    if (map_data.empty()) {
        std::cerr << "地图数据为空" << std::endl;
        return;
    }

    int height = map_data.size();
    int width = map_data[0].size();

    if (!costmap_) {
        costmap_ = new Costmap2D(width, height, resolution, origin_x, origin_y);
    } else {
        *costmap_ = Costmap2D(width, height, resolution, origin_x, origin_y);
    }

    // 复制地图数据
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            costmap_->setCost(x, y, map_data[y][x]);
        }
    }

    resolution_ = resolution;

    std::cout << "从数据设置地图成功" << std::endl;
    std::cout << "地图尺寸: " << width << "x" << height << std::endl;
    std::cout << "分辨率: " << resolution << " m/pixel" << std::endl;
    std::cout << "原点: (" << origin_x << ", " << origin_y << ")" << std::endl;
}

// 回调函数设置实现
void RRTstarPlannerNoRos::setPathPublishCallback(std::function<void(const std::vector<PoseStamped>&)> callback) {
    path_callback_ = callback;
    plan_pub_.setCallback(callback);
}

void RRTstarPlannerNoRos::setMarkerPublishCallback(std::function<void(const Marker&)> callback) {
    marker_callback_ = callback;
    marker_pub_.setCallback(callback);
}

void RRTstarPlannerNoRos::setAccessablePublishCallback(std::function<void(bool)> callback) {
    accessable_callback_ = callback;
    accessable_pub_.setCallback(callback);
}
