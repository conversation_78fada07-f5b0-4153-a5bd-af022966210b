#pragma once

#if COMMUNICATION_TYPE == ROS1
#include <ros/ros.h>
#include <sensor_msgs/Image.h>
#include "subscriber_base.h"
namespace communication::ros1 {
class ImageDataSubscriberRos1 : public ImageDataSubscriberBase {
 public:
  ImageDataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic,
                                size_t max_buffer_size = 10) : ImageDataSubscriberBase(topic, max_buffer_size), nh_(nh) {

    subscriber_ = nh_.subscribe(topic, max_buffer_size, &ImageDataSubscriberRos1::ImageDataCallbackRos1, this);
  }

  ~ImageDataSubscriberRos1() = default;

  void ImageDataCallbackRos1(const sensor_msgs::Image::ConstPtr &image_msg){
    // Convert the ROS message to cv::Mat and store it in the buffer
    cv::Mat image;
    try {
      image = cv_bridge::toCvCopy(camera_info_msg, sensor_msgs::image_encodings::TYPE_16UC1)->image;
    } catch (const cv_bridge::Exception &e) {
      ROS_ERROR("cv_bridge exception: %s", e.what());
      return;
    }

    std::lock_guard<std::mutex> lock(buffer_mutex_);
    data_buffer_.push_back(image);
    if (data_buffer_.size() > max_buffer_size_) {
      data_buffer_.pop_front(); // Remove oldest data if buffer exceeds max size
    }
  }

 private:
  ros::NodeHandle &nh_;
  ros::Subscriber subscriber_;
};
} // namespace communication::ros1
#endif // COMMUNICATION_TYPE == ROS1