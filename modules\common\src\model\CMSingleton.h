#ifndef _CM_SINGLETON_H_
#define _CM_SINGLETON_H_

#include "CM_pubhead.h"
#include "CMMutex.h"

template<class SingletonClass>
class CMSingleton
{
public:
    CMSingleton()
    {
        m_s_pSingletonInstance = NULL;
        m_s_bIsDestroyed = false;
    }

    virtual ~CMSingleton()
    {
        m_s_pSingletonInstance = NULL;
        m_s_bIsDestroyed = true;
    }

    static SingletonClass* GetSingletonInstance()
    {
        ASSERT(!m_s_bIsDestroyed);

        if (!m_s_pSingletonInstance)
        {
            m_s_cs.Lock();
            if (!m_s_pSingletonInstance)
            {
                m_s_pSingletonInstance = new SingletonClass;
            }
            m_s_cs.Unlock();
        }

        return m_s_pSingletonInstance;
    }

private:
    static SingletonClass* m_s_pSingletonInstance;
    static CMMutex m_s_cs;
    static cx_bool m_s_bIsDestroyed;
};

template<class SingletonClass> 	SingletonClass*  CMSingleton<SingletonClass>::m_s_pSingletonInstance = NULL;
template<class SingletonClass> 	CMMutex CMSingleton<SingletonClass>::m_s_cs;
template<class SingletonClass> 	bool CMSingleton<SingletonClass>::m_s_bIsDestroyed = false;

#endif // _CM_SINGLETON_H_
