#include <memory>
#include <thread>
#include <iomanip>
#include "communication.h"

std::shared_ptr<communication::ImuDataSubscriberBase> imu_subscriber_ptr;
std::shared_ptr<communication::GnssDataSubscriberBase> gnss_subscriber_ptr;
std::shared_ptr<communication::LidarDataSubscriberBase> lidar_subscriber_ptr;

std::shared_ptr<communication::OdometrySubscriberBase> odometry_subscriber_ptr;
std::shared_ptr<communication::IntDataSubscriberBase> int_data_subscriber_ptr;
std::shared_ptr<communication::DoubleDataSubscriberBase> double_data_subscriber_ptr;
std::shared_ptr<communication::StringDataSubscriberBase> string_data_subscriber_ptr;
std::shared_ptr<communication::BoolDataSubscriberBase> bool_data_subscriber_ptr;
std::shared_ptr<communication::CloudDataSubscriberBase> cloud_data_subscriber_ptr;
std::shared_ptr<communication::PathDataSubscriberBase> path_data_subscriber_ptr;
std::shared_ptr<communication::PoseDataSubscriberBase> pose_data_subscriber_ptr;

std::shared_ptr<communication::OdometryPublisherBase> odometry_publisher_ptr;
std::shared_ptr<communication::IntDataPublisherBase> int_data_publisher_ptr;
std::shared_ptr<communication::DoubleDataPublisherBase> double_data_publisher_ptr;
std::shared_ptr<communication::StringDataPublisherBase> string_data_publisher_ptr;
std::shared_ptr<communication::BoolDataPublisherBase> bool_data_publisher_ptr;
std::shared_ptr<communication::CloudDataPublisherBase> cloud_data_publisher_ptr;
std::shared_ptr<communication::PathDataPublisherBase> path_data_publisher_ptr;
std::shared_ptr<communication::PoseDataPublisherBase> pose_data_publisher_ptr;

// 数据接收线程
void DataReceiveThread() {
  while (true) {
     // Example usage of subscribers and publishers
    if (!imu_subscriber_ptr->IsBufferEmpty()) {
      auto imu_data = imu_subscriber_ptr->GetBuffer();
      std::cout << std::fixed << std::setprecision(3) << "Received IMU data: " << imu_data.front().time << std::endl;
      // Process IMU data...
    }
    if (!gnss_subscriber_ptr->IsBufferEmpty()) {
      auto gnss_data = gnss_subscriber_ptr->GetBuffer();
      std::cout << std::fixed << std::setprecision(3) << "Received GNSS data: " << gnss_data.front().time << std::endl;
      // Process GNSS data...
    }
    if (!lidar_subscriber_ptr->IsBufferEmpty()) {
      auto lidar_data = lidar_subscriber_ptr->GetBuffer();
      std::cout << std::fixed << std::setprecision(3)
                << "Received LiDAR data: " << lidar_data.front().time
                << ", size:" << lidar_data.front().cloud_ptr->points.size()
                << ", first point: ("
                << lidar_data.front().cloud_ptr->points[0].x << ", "
                << lidar_data.front().cloud_ptr->points[0].y << ", "
                << lidar_data.front().cloud_ptr->points[0].z << ")"
                << ", intensity: " << lidar_data.front().cloud_ptr->points[0].intensity
                << ", ring: " << static_cast<int>(lidar_data.front().cloud_ptr->points[0].ring)
                << "; last point: ("
                << lidar_data.front().cloud_ptr->points[lidar_data.front().cloud_ptr->points.size() - 1].x << ", "
                << lidar_data.front().cloud_ptr->points[lidar_data.front().cloud_ptr->points.size() - 1].y << ", "
                << lidar_data.front().cloud_ptr->points[lidar_data.front().cloud_ptr->points.size() - 1].z << ")"
                << ", intensity: " << lidar_data.front().cloud_ptr->points[lidar_data.front().cloud_ptr->points.size() - 1].intensity
                << ", ring: " << static_cast<int>(lidar_data.front().cloud_ptr->points[lidar_data.front().cloud_ptr->points.size() - 1].ring)
                << std::endl;
      // Process LiDAR data...
    }

    if (!odometry_subscriber_ptr->IsBufferEmpty()) {
      auto odometry_data = odometry_subscriber_ptr->GetBuffer();
      std::cout << std::fixed << std::setprecision(3)
                << "time: " << odometry_data.front().pose_data.time << ", "
                << ", Received odometry data: " 
                << odometry_data.front().pose_data.position[0] << ", "
                << odometry_data.front().pose_data.position[1] << ", "
                << odometry_data.front().pose_data.position[2] << std::endl;
      // Process odometry data...
    }

    if (!int_data_subscriber_ptr->IsBufferEmpty()) {
      auto int_data = int_data_subscriber_ptr->GetBuffer();
      std::cout << std::fixed << std::setprecision(3)
                << "time:" << std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count()
                << ", Received integer data: " 
                << int_data.front() << std::endl;
      // Process integer data...
    }

    if (!double_data_subscriber_ptr->IsBufferEmpty()) {
      auto double_data = double_data_subscriber_ptr->GetBuffer();
      std::cout << std::fixed << std::setprecision(3)
      << "time:" << std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count()
      << ", Received double data: " << double_data.front() << std::endl;
      // Process double data...
    }
    
    if (!string_data_subscriber_ptr->IsBufferEmpty()) {
      auto string_data = string_data_subscriber_ptr->GetBuffer();
      std::cout << std::fixed << std::setprecision(3)
      << "time:" << std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count()
      << ", Received string data: " << string_data.front() << std::endl;
      // Process string data...
    
    }

    if (!bool_data_subscriber_ptr->IsBufferEmpty()) {
      auto bool_data = bool_data_subscriber_ptr->GetBuffer();
      std::cout << std::fixed << std::setprecision(3)
      << "time:" << std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count()
      << ", Received boolean data: " << std::boolalpha << bool_data.front() << std::endl;
      // Process boolean data...
    }

    if (!cloud_data_subscriber_ptr->IsBufferEmpty()) {
      auto cloud_data = cloud_data_subscriber_ptr->GetBuffer();
      std::cout << std::fixed << std::setprecision(3)
      << "time:" << cloud_data.front().time
      << ", Received cloud data with " << cloud_data.front().cloud_ptr->points.size() << " points" 
      << ", first point: ("
      << cloud_data.front().cloud_ptr->points[0].x << ", "
      << cloud_data.front().cloud_ptr->points[0].y << ", "
      << cloud_data.front().cloud_ptr->points[0].z << ")"
      << ", intensity: " << cloud_data.front().cloud_ptr->points[0].intensity
      << std::endl;
      // Process cloud data...
    }
    if (!path_data_subscriber_ptr->IsBufferEmpty()) {
      auto path_data = path_data_subscriber_ptr->GetBuffer();
      std::cout << std::fixed << std::setprecision(3)
      << "time:" << path_data.front().time_
      << ", Received path data with " << path_data.front().poses_.size() << " poses." << std::endl;
      // Process path data...
    }

    if (!pose_data_subscriber_ptr->IsBufferEmpty()) {
      auto pose_data = pose_data_subscriber_ptr->GetBuffer();
      std::cout << std::fixed << std::setprecision(3)
      << "time:" << pose_data.front().time
      << ", Received pose data: "
      << pose_data.front().position[0] << ", "
      << pose_data.front().position[1] << ", "
      << pose_data.front().position[2] << ", "
      << pose_data.front().orientation[0] << ", "
      << pose_data.front().orientation[1] << ", "
      << pose_data.front().orientation[2] << ", "
      << pose_data.front().orientation[3]
      << std::endl;
      // Process pose data...
    }
    

    // Simulate some work being done in this thread
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
  }
}

// 数据发送线程
void DataSendThread() {
  while (true) {
    // Example usage of publishers
   // if (odometry_publisher_ptr->GetSubscriberCount() > 0) {
      communication::PoseVelData odometry_data;
      odometry_data.pose_data.time = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count();
      odometry_data.pose_data.position[0] = 1.0;
      odometry_data.pose_data.position[1] = 2.0;
      odometry_data.pose_data.position[2] = 3.0;
      odometry_data.pose_data.orientation[0] = 0.0;
      odometry_data.pose_data.orientation[1] = 0.0;
      odometry_data.pose_data.orientation[2] = 0.0;
      odometry_data.pose_data.orientation[3] = 1.0;
      odometry_data.vel[0] = 0.1;
      odometry_data.vel[1] = 0.2;
      odometry_data.vel[2] = 0.3;
      odometry_data.angle_vel[0] = 0.01;
      odometry_data.angle_vel[1] = 0.02;
      odometry_data.angle_vel[2] = 0.03;
      
      odometry_publisher_ptr->Publish(odometry_data);
  //  }

    //if (int_data_publisher_ptr->GetSubscriberCount() > 0) {
      int_data_publisher_ptr->Publish(100); // Example integer value to publish
   // }

   // if (double_data_publisher_ptr->GetSubscriberCount() > 0) {
      double_data_publisher_ptr->Publish(3.14); // Example double value to publish
   // }

   // if (string_data_publisher_ptr->GetSubscriberCount() > 0) {
      string_data_publisher_ptr->Publish("Hello, World!"); // Example string value to publish
  //  }

  //  if (bool_data_publisher_ptr->GetSubscriberCount() > 0) {
      bool_data_publisher_ptr->Publish(true); // Example boolean value to publish
   // }

   // if (cloud_data_publisher_ptr->GetSubscriberCount() > 0) {
      communication::CloudData cloud_data;
      cloud_data.time = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count();
      cloud_data.cloud_ptr.reset(new pcl::PointCloud<pcl::PointXYZI>());
      communication::CloudData::POINT point;
      point.x = 1.0;
      point.y = 2.0;
      point.z = 3.0;
      point.intensity = 1.0; // Example intensity value
      cloud_data.cloud_ptr->points.push_back(point);
      // cloud_data.cloud_ptr->points.push_back(pcl::PointXYZI(1.0, 2.0, 3.0, 1.0));
      cloud_data_publisher_ptr->Publish(cloud_data);
   // }

   // if (path_data_publisher_ptr->GetSubscriberCount() > 0) {
      communication::PathData path_data;
      path_data.time_ = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count();
      communication::PoseData pose;
      pose.time = path_data.time_;
      pose.position = Eigen::Vector3f(1.0, 2.0, 3.0);
      pose.orientation = Eigen::Vector4f(0.0, 0.0, 0.0, 1.0);
      path_data.poses_.push_back(pose);
      path_data_publisher_ptr->Publish(path_data);
    //}

    //if (pose_data_publisher_ptr->GetSubscriberCount() > 0) {
      communication::PoseData pose_data;
      pose_data.time = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count();
      pose_data.position = Eigen::Vector3f(1.0, 2.0, 3.0);
      pose_data.orientation = Eigen::Vector4f(0.0, 0.0, 0.0, 1.0);
      pose_data_publisher_ptr->Publish(pose_data);
  //}
    // Simulate some work being done in this thread
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }
}


int main(int argc, char **argv) {

  std::string config_file_path = "";
  if(argc > 1){
    config_file_path = argv[1];
  }

  auto communication_ptr = std::make_shared<communication::Communication>("localization_node");

  if (!communication_ptr->Initialize(config_file_path)) {
    std::cerr << "Failed to initialize communication." << std::endl;
    return -1;
  }

  // Create subscribers for IMU, GNSS, and LiDAR data
  imu_subscriber_ptr = communication_ptr->CreateImuDataSubscriber("/imu");
  gnss_subscriber_ptr = communication_ptr->CreateGnssDataSubscriber("/chattergps");
  lidar_subscriber_ptr = communication_ptr->CreateLidarDataSubscriber("/lslidar_point_cloud");

  odometry_subscriber_ptr = communication_ptr->CreateOdometrySubscriber("/odometry");
  int_data_subscriber_ptr = communication_ptr->CreateIntDataSubscriber("/int/data");
  double_data_subscriber_ptr = communication_ptr->CreateDoubleDataSubscriber("/double/data");
  string_data_subscriber_ptr = communication_ptr->CreateStringDataSubscriber("/string/data");
  bool_data_subscriber_ptr = communication_ptr->CreateBoolDataSubscriber("/bool/data");
  cloud_data_subscriber_ptr = communication_ptr->CreateCloudDataSubscriber("/cloud/data");
  path_data_subscriber_ptr = communication_ptr->CreatePathDataSubscriber("/path/data");
  pose_data_subscriber_ptr = communication_ptr->CreatePoseDataSubscriber("/pose/data");

  odometry_publisher_ptr = communication_ptr->CreateOdometryPublisher("/odometry", "map", "body", 10);
  int_data_publisher_ptr = communication_ptr->CreateIntDataPublisher("/int/data", 10);
  double_data_publisher_ptr = communication_ptr->CreateDoubleDataPublisher("/double/data", 10);
  string_data_publisher_ptr = communication_ptr->CreateStringDataPublisher("/string/data", 10);
  bool_data_publisher_ptr = communication_ptr->CreateBoolDataPublisher("/bool/data", 10);
  cloud_data_publisher_ptr = communication_ptr->CreateCloudDataPublisher("/cloud/data", "map", 10);
  path_data_publisher_ptr = communication_ptr->CreatePathDataPublisher("/path/data", "map", 10);
  pose_data_publisher_ptr = communication_ptr->CreatePoseDataPublisher("/pose/data", 10);


  // Start the data receive thread
  std::thread data_receive_thread(&DataReceiveThread);
  data_receive_thread.detach(); // Detach the thread to run independently

  std::thread data_send_thread(&DataSendThread);
  data_send_thread.detach(); // Detach the thread to run independently

  communication_ptr->Run();

  return 0;
}


