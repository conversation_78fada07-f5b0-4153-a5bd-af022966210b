#ifndef POINT_PUBLISH_H
#define POINT_PUBLISH_H

#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <fstream>

using namespace std;

// 去ROS化的消息结构体
namespace point_publish_no_ros {

/**
 * @brief 时间戳结构
 */
struct TimeStamp {
    double sec;
    
    TimeStamp() : sec(0.0) {}
    TimeStamp(double s) : sec(s) {}
    
    static TimeStamp now() {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        double seconds = std::chrono::duration<double>(duration).count();
        return TimeStamp(seconds);
    }
    
    double toSec() const { return sec; }
};

/**
 * @brief 消息头结构
 */
struct Header {
    TimeStamp stamp;
    std::string frame_id;
    
    Header() : frame_id("") {}
    
    void setCurrentTime() {
        stamp = TimeStamp::now();
    }
};

/**
 * @brief 3D点结构
 */
struct Point {
    double x, y, z;
    
    Point() : x(0.0), y(0.0), z(0.0) {}
    Point(double _x, double _y, double _z) : x(_x), y(_y), z(_z) {}
};

/**
 * @brief 四元数结构
 */
struct Quaternion {
    double x, y, z, w;
    
    Quaternion() : x(0.0), y(0.0), z(0.0), w(1.0) {}
    Quaternion(double _x, double _y, double _z, double _w) : x(_x), y(_y), z(_z), w(_w) {}
    
    // 从欧拉角创建四元数
    static Quaternion fromRPY(double roll, double pitch, double yaw) {
        Quaternion q;
        double cy = cos(yaw * 0.5);
        double sy = sin(yaw * 0.5);
        double cp = cos(pitch * 0.5);
        double sp = sin(pitch * 0.5);
        double cr = cos(roll * 0.5);
        double sr = sin(roll * 0.5);

        q.w = cr * cp * cy + sr * sp * sy;
        q.x = sr * cp * cy - cr * sp * sy;
        q.y = cr * sp * cy + sr * cp * sy;
        q.z = cr * cp * sy - sr * sp * cy;
        
        return q;
    }
    
    // 转换为欧拉角
    void getRPY(double& roll, double& pitch, double& yaw) const {
        // Roll (x-axis rotation)
        double sinr_cosp = 2 * (w * x + y * z);
        double cosr_cosp = 1 - 2 * (x * x + y * y);
        roll = atan2(sinr_cosp, cosr_cosp);

        // Pitch (y-axis rotation)
        double sinp = 2 * (w * y - z * x);
        if (abs(sinp) >= 1)
            pitch = copysign(M_PI / 2, sinp); // use 90 degrees if out of range
        else
            pitch = asin(sinp);

        // Yaw (z-axis rotation)
        double siny_cosp = 2 * (w * z + x * y);
        double cosy_cosp = 1 - 2 * (y * y + z * z);
        yaw = atan2(siny_cosp, cosy_cosp);
    }
};

/**
 * @brief 位姿结构
 */
struct Pose {
    Point position;
    Quaternion orientation;
    
    Pose() {}
    Pose(double x, double y, double z, double roll, double pitch, double yaw) 
        : position(x, y, z), orientation(Quaternion::fromRPY(roll, pitch, yaw)) {}
};

/**
 * @brief 带时间戳的位姿
 */
struct PoseStamped {
    Header header;
    Pose pose;
    
    PoseStamped() {}
    PoseStamped(double x, double y, double z, double roll, double pitch, double yaw) 
        : pose(x, y, z, roll, pitch, yaw) {
        header.setCurrentTime();
    }
};

/**
 * @brief 导航目标消息 (完全保留原有字段)
 */
struct NavigationTarget {
    int32_t nav_mode;
    int32_t point_id;
    double pose_x, pose_y, pose_z;
    double yaw;
    int32_t point_info;
    int32_t gait;
    int32_t speed;
    int32_t manner;
    int32_t obsmode;
    int32_t navmode;
    
    NavigationTarget() : nav_mode(0), point_id(0), pose_x(0.0), pose_y(0.0), pose_z(0.0),
                        yaw(0.0), point_info(0), gait(0), speed(1), manner(0), obsmode(1), navmode(0) {}
};

/**
 * @brief 导航结果消息
 */
struct NavigationResult {
    int32_t point_id;
    double target_pose_x, target_pose_y, target_pose_z;
    double target_yaw;
    double current_pose_x, current_pose_y, current_pose_z;
    double current_yaw;
    int32_t nav_state;
    
    NavigationResult() : point_id(0), target_pose_x(0.0), target_pose_y(0.0), target_pose_z(0.0),
                        target_yaw(0.0), current_pose_x(0.0), current_pose_y(0.0), current_pose_z(0.0),
                        current_yaw(0.0), nav_state(0) {}
};

/**
 * @brief 发布者模板
 */
template<typename T>
class Publisher {
public:
    Publisher() {}
    
    void publish(const T& msg) {
        if (callback_) {
            auto msg_ptr = std::make_shared<T>(msg);
            callback_(msg_ptr);
        }
    }
    
    void setCallback(std::function<void(const std::shared_ptr<T>&)> callback) {
        callback_ = callback;
    }

private:
    std::function<void(const std::shared_ptr<T>&)> callback_;
};

/**
 * @brief 订阅者模板
 */
template<typename T>
class Subscriber {
public:
    Subscriber() {}
    
    void setCallback(std::function<void(const std::shared_ptr<const T>&)> callback) {
        callback_ = callback;
    }
    
    void inputMessage(const T& msg) {
        if (callback_) {
            auto msg_ptr = std::make_shared<const T>(msg);
            callback_(msg_ptr);
        }
    }

private:
    std::function<void(const std::shared_ptr<const T>&)> callback_;
};

} // namespace point_publish_no_ros

/**
 * @brief 去ROS化的点发布器类
 */
class PointPublishNoRos {
public:
    PointPublishNoRos(const std::string& name);
    ~PointPublishNoRos();
    
    // 初始化和控制
    bool init();
    bool initFromConfig(const std::string& config_file = "");
    void start();
    void stop();
    
    // 参数设置 (完全保留原有参数)
    void setParameters(int point_id, int point_info, int gait, int speed, 
                      int manner, int obsmode, int navmode);
    void setPointId(int point_id);
    void setPointInfo(int point_info);
    void setGait(int gait);
    void setSpeed(int speed);
    void setManner(int manner);
    void setObsmode(int obsmode);
    void setNavmode(int navmode);
    
    // 输入函数 (替换ROS订阅)
    void inputGoal(const point_publish_no_ros::PoseStamped& goal);
    void inputWebGoal(const point_publish_no_ros::PoseStamped& goal);
    
    // 回调函数设置 (替换ROS发布)
    void setNavigationTargetCallback(std::function<void(const std::shared_ptr<point_publish_no_ros::NavigationTarget>&)> callback);
    void setNavigationResultCallback(std::function<void(const std::shared_ptr<point_publish_no_ros::NavigationResult>&)> callback);
    
    // 状态查询
    bool isInitialized() const;
    bool isRunning() const;
    
    // 获取当前参数
    int getPointId() const;
    int getPointInfo() const;
    int getGait() const;
    int getSpeed() const;
    int getManner() const;
    int getObsmode() const;
    int getNavmode() const;
    
    // 配置管理
    bool loadConfiguration(const std::string& config_file);
    bool saveConfiguration(const std::string& config_file) const;
    void printStatus() const;

private:
    // 成员变量
    std::string name_;
    bool initialized_;
    std::atomic<bool> running_;
    std::thread worker_thread_;
    std::mutex data_mutex_;
    
    // 原有的所有变量 (完全保留)
    int point_id_;
    int point_info_;
    int gait_;
    int speed_;
    int manner_;
    int obsmode_;
    int navmode_;
    
    // 回调函数
    std::function<void(const std::shared_ptr<point_publish_no_ros::NavigationTarget>&)> nav_target_callback_;
    std::function<void(const std::shared_ptr<point_publish_no_ros::NavigationResult>&)> nav_result_callback_;
    
    // 原有的处理函数 (完全保留实现)
    void goalHandler(const std::shared_ptr<const point_publish_no_ros::PoseStamped>& goal);
    void webgoalHandler(const std::shared_ptr<const point_publish_no_ros::PoseStamped>& goal);
    
    // 内部函数
    void controlLoop();
    void setDefaultParameters();
    bool loadConfigurationFromYAML(const std::string& yaml_file);
    bool loadConfigurationFromText(const std::string& config_file);
};

#endif // POINT_PUBLISH_H
