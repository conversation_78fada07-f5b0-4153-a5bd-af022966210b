/**
 * 经纬高与东北天坐标转换
 */
#pragma once

namespace common_lib{

    namespace lla2enu{

        void GetOrigin(double &latitude, double &longitude, double &altitude);

        void Reset(double latitude, double longitude, double altitude);

        void Forward(double latitude, double longitude, double altitude,
                     double &local_e, double &local_n, double &local_u);

        void Reverse(double local_e, double local_n, double local_u, double &latitude,
                     double &longitude, double &altitude);

    } // namespace lla2enu
} // namespace common_lib
