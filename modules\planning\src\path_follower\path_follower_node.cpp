#include "path_follower.h"
#include <iostream>
#include <signal.h>
#include <thread>
#include <iomanip> 

using namespace path_follower;

// 全局变量用于信号处理
PathFollowerNoRos* g_follower = nullptr;
bool g_running = true;

// 信号处理函数
void signalHandler(int signum) {
    std::cout << "\n收到信号 " << signum << "，正在关闭路径跟随器..." << std::endl;
    g_running = false;
    if (g_follower) {
        g_follower->stopFollowing();
    }
}

int main(int argc, char** argv) {
    std::cout << "=== 路径跟随器节点启动 ===" << std::endl;
    
    // 注册信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    try {
        // 解析命令行参数
        std::string config_path = "../config/path_follower.yaml";
        if (argc > 1) {
            config_path = argv[1];
            std::cout << "使用配置文件: " << config_path << std::endl;
        } else {
            std::cout << "使用默认配置" << std::endl;
        }
        
        // 创建路径跟随器
        std::cout << "\n🚀 创建路径跟随器..." << std::endl;
        PathFollowerNoRos follower(config_path);
        g_follower = &follower;
        
        // 设置输出回调函数
        std::cout << "\n📡 设置回调函数..." << std::endl;
        
        // 速度命令发布回调
        follower.setSpeedPublishCallback([](const std::shared_ptr<TwistData>& cmd_vel) {
            std::cout << "发布速度命令: linear.x=" << std::fixed << std::setprecision(3) 
                     << cmd_vel->linear.x << " m/s, angular.z=" << cmd_vel->angular.z 
                     << " rad/s" << std::endl;
        });
        
        // 初始化跟随器
        std::cout << "\n🔧 初始化路径跟随器..." << std::endl;
        if (!follower.initialize()) {
            std::cerr << "❌ 路径跟随器初始化失败！" << std::endl;
            return -1;
        }
        
        // 启动跟随器
        std::cout << "\n▶️  启动路径跟随器..." << std::endl;
        follower.startFollowing();
        
        // 打印状态信息
        std::cout << "\n📊 跟随器状态:" << std::endl;
        follower.printStatus();
        
        std::cout << "\n✅ 路径跟随器节点启动成功！" << std::endl;
        std::cout << "按 Ctrl+C 退出程序" << std::endl;
        
        // 主循环
        const double loop_rate = 100.0; // 100Hz
        const auto sleep_duration = std::chrono::duration<double>(1.0 / loop_rate);
        
        while (g_running && follower.isRunning()) {
            try {
                // 处理一次路径跟随
                follower.processOnce();
                
                // 控制循环频率
                std::this_thread::sleep_for(sleep_duration);
            }
            catch (const std::exception& e) {
                std::cerr << "跟随循环异常: " << e.what() << std::endl;
                break;
            }
        }
        
        // 停止跟随器
        std::cout << "\n⏹️  停止路径跟随器..." << std::endl;
        follower.stopFollowing();
        
        std::cout << "✅ 路径跟随器节点已安全退出" << std::endl;
        
    }
    catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return -1;
    }
    catch (...) {
        std::cerr << "❌ 未知异常" << std::endl;
        return -1;
    }
    
    return 0;
}
