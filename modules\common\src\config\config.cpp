#include "config.h"
#include "DataType.h"
#include <iostream>

bool Config::init(const std::string& config_file) 
{
    if (m_bInitialized) {
        std::cout << "Config already initialized" << std::endl;
        return true;
    }

    try {
        loadParameters(config_file);
        m_bInitialized = true;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize config: " << e.what() << std::endl;
        return false;
    }
}

template<typename T>
T Config::getParam(const YAML::Node& node, const std::string& key, const T& default_value) 
{
    try {
        return node[key].as<T>();
    } catch (...) {
        return default_value;
    }
}

void Config::loadParameters(const std::string& config_file)
{
    try {
        YAML::Node config = YAML::LoadFile(config_file);

        // Parent directory
        m_strParentDir = getParam<std::string>(config, "parent_dir",
            "/mnt/d/work/01_code/jianmi/20250319/na_localization_lslidar/src/na_localization/");
        
        // Map related parameters
        m_strMapDir = getParam<std::string>(config, "map_dir", "PCD/");
        m_strMapDir = m_strParentDir + m_strMapDir;
        
        m_strMapName = getParam<std::string>(config, "map_name", "cloud_map.pcd");
        m_strLoadMapPath = m_strMapDir + m_strMapName;
        
        m_strPoseTxtName = getParam<std::string>(config, "pose_txt_name", "pose.txt");
        m_strLoadPosePath = m_strMapDir + m_strPoseTxtName;

        // Log parameters
        m_strLog4cplusFilePath = getParam<std::string>(config, "log4cplus_file_path", "config/log4cplus.properties");
        m_strLog4cplusFilePath = m_strParentDir + m_strLog4cplusFilePath;
        m_strLog4cplusOutfilePath = getParam<std::string>(config, "log4cplus_outfile_path", "");

        // Publishing flags
        const auto& publish = config["publish"];
        m_bPathEn = getParam<bool>(publish, "path_en", true);
        m_bScanPubEn = getParam<bool>(publish, "scan_publish_en", true);
        m_bDensePubEn = getParam<bool>(publish, "dense_publish_en", true);
        m_bScanBodyPubEn = getParam<bool>(publish, "scan_bodyframe_pub_en", true);
        m_bVisualIkdtree = getParam<bool>(publish, "visual_ikdtree", true);

        // Algorithm parameters
        m_nNumMaxIterations = getParam<int>(config, "max_iteration", 4);
        m_strMapFilePath = getParam<std::string>(config, "map_file_path", "");

        // Topics
        const auto& common = config["common"];
        m_strLidTopic = getParam<std::string>(common, "lid_topic", "/livox/lidar");
        m_strImuTopic = getParam<std::string>(common, "imu_topic", "/livox/imu");
        m_strGnssTopic = getParam<std::string>(common, "gnss_topic", "/rtk_pos_raw");
        m_strGnssHeadingTopic = getParam<std::string>(common, "gnss_heading_topic", "/rtk_heading_raw");
        m_strLegTopic = getParam<std::string>(common, "leg_topic", "/leg_odom");
        m_strManualPosTopic = getParam<std::string>(common, "manual_pos_topic", "/initialpose");

        // Time sync
        m_bTimeSyncEn = getParam<bool>(common, "time_sync_en", false);
        m_dTimeDiffLidarToImu = getParam<double>(common, "time_offset_lidar_to_imu", 0.0);

        // Filter sizes
        m_dFilterSizeCornerMin = getParam<double>(config, "filter_size_corner", 0.5);
        m_dFilterSizeSurfMin = getParam<double>(config, "filter_size_surf", 0.5);
        m_dFilterSizeMapMin = getParam<double>(config, "filter_size_map", 0.5);

        // Mapping parameters
        m_dCubeLen = getParam<double>(config, "cube_side_length", 200);
        const auto& mapping = config["mapping"];
        m_fDetRange = getParam<float>(mapping, "det_range", 300.f);
        m_dFovDeg = getParam<double>(mapping, "fov_degree", 180);
        m_dGyrCov = getParam<double>(mapping, "gyr_cov", 0.1);
        m_dAccCov = getParam<double>(mapping, "acc_cov", 0.1);
        m_dBGyrCov = getParam<double>(mapping, "b_gyr_cov", 0.0001);
        m_dBAccCov = getParam<double>(mapping, "b_acc_cov", 0.0001);

        // Preprocessing parameters
        const auto& preprocess = config["preprocess"];
        m_dBlind = getParam<double>(preprocess, "blind", 0.01);
        m_nLidarType = getParam<int>(preprocess, "lidar_type", VELO16);
        m_nScans = getParam<int>(preprocess, "scan_line", 16);
        m_nTimeUnit = getParam<int>(preprocess, "timestamp_unit", SEC);
        m_nScanRate = getParam<int>(preprocess, "scan_rate", 10);
        m_nPointFilterNum = getParam<int>(config, "point_filter_num", 1);
        m_bFeatureEnabled = getParam<bool>(config, "feature_extract_enable", false);

        // Extrinsic parameters
        m_bExtrinsicEstEn = getParam<bool>(mapping, "extrinsic_est_en", false);
        m_vecExtrinT = getParam<std::vector<double>>(mapping, "extrinsic_T", std::vector<double>());
        m_vecExtrinR = getParam<std::vector<double>>(mapping, "extrinsic_R", std::vector<double>());
        m_vecRtk2LidarT = getParam<std::vector<double>>(mapping, "rtk2Lidar_T", std::vector<double>());

        // Reloc parameters
        m_dSearchRadius = getParam<double>(config, "Search_Radius", 5.0);
        m_dSearchRadiusRtk = getParam<double>(config, "Search_Radius_rtk", 15.0);

        // RTK parameters
        m_bUseRtk = getParam<bool>(config, "usertk", false);
        m_nStatusThreshold = getParam<int>(config, "status_threshold", 4);

        // IMU parameters
        m_strImuAxis = getParam<std::string>(config, "imu_axis", "FLU");

        // Zero velocity detection
        const auto& zero_detect = config["zero_detect"];
        m_dAccStdThreshold = getParam<double>(zero_detect, "acc_std_threshold", 0.0024);
        m_dGyroStdThreshold = getParam<double>(zero_detect, "gyro_std_threshold", 0.00034);
        m_bZeroDetectEnable = getParam<bool>(zero_detect, "enable", false);

        // Matching parameters
        m_dMatchRate = getParam<double>(config, "match_rate", 75.0);

         // reloc param
        const auto& reloc = config["reloc"];
        m_dMatchRateThreshold = getParam<double>(reloc, "match_rate_threshold", 0.75);
        m_dLocalMapRadius = getParam<double>(reloc, "local_map_radius", 100);

    } catch (const YAML::Exception& e) {
        std::cerr << "Error reading config file: " << e.what() << std::endl;
        throw;
    }
}