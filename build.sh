#!/bin/bash

# Support platform: x86_64
# Build type: debug; release
# Communication type: ros1

platform_list=(
  x86_64
)

build_type_list=(
  debug
  release
)

communication_type_list=(
  ros1
)

function check_platform() {
  found=1
  for item in "${platform_list[@]}"; do
    if [ "${item}" == "$1" ]; then
      found=0
      break
    fi
  done

  echo $found
}

function check_build_type() {
  found=1
  for item in "${build_type_list[@]}"; do
    if [ "${item}" == "$1" ]; then
      found=0
      break
    fi
  done
  
  echo $found
}

function check_communication_type() {
  found=1
  for item in "${communication_type_list[@]}"; do
    if [ "${item}" == "$1" ]; then
      found=0
      break
    fi
  done

  echo $found 
}

function print_platform_and_build_type {
  echo "Supported platform: ${platform_list[@]}"
  echo "Supported build type: ${build_type_list[@]}"
  echo "Supported communication type: ${communication_type_list[@]}"
}

current_dir=$(cd `dirname $0`; pwd)
echo "Current directory: $current_dir"

# Set defualt platform x86_64
platform=$1
if [ -z "$platform" ]; then
  platform=x86_64
fi

# Set defualt build type debug
build_type=$2
if [ -z "$build_type" ]; then
  build_type=debug
fi

# Set default communication type
commnunication_type=$3
if [ -z "$commnunication_type" ]; then
  commnunication_type=ros1
fi

echo "Platform: ${platform}"
echo "Build type: ${build_type}"
echo "Commnuication type: ${commnunication_type}"

if [ $(check_platform $platform) -eq 1 ]; then
  echo "Platform: ${platform} is not supported!"
  print_platform_and_build_type
  exit 1
fi

if [ $(check_build_type $build_type) -eq 1 ]; then
  echo "Build type: ${build_type} is not supported!"
  print_platform_and_build_type
  exit 1
fi

if [ $(check_communication_type $commnunication_type) -eq 1 ]; then
  echo "Build type: ${commnunication_type} is not supported!"
  print_platform_and_build_type
  exit 1
fi

build_dir="${current_dir}/build_${platform}_${build_type}_${commnunication_type}"

if [ ! -d $build_dir ]; then
  mkdir $build_dir
fi

if [ "${commnunication_type}" == "ros1" ];then
  commnunication_type=ROS1
fi

cd $build_dir 

cmake -DCMAKE_BUILD_TYPE=$build_type -DCOMMUNICATION_TYPE=${commnunication_type} .. 
if [ $? -ne 0 ]; then
  exit 1
fi

make -j8
if [ $? -ne 0 ]; then
  exit 1
fi
