#include "velocity_smoother.h"
#include <iostream>
#include <iomanip>
#include <fstream>
#include <chrono>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <random>  // Add random number generation headers

using namespace yocs_velocity_smoother;

/**
 * @brief 速度命令生成器 - 模拟不同的速度输入模式
 */
class VelocityCommandGenerator {
public:
    VelocityCommandGenerator() : rng_(std::random_device{}()), dist_(-1.0, 1.0) {}

    // 生成阶跃速度命令
    TwistMsg generateStepCommand(double linear_v, double angular_w) {
        return TwistMsg(linear_v, angular_w);
    }

    // 生成随机速度命令
    TwistMsg generateRandomCommand(double max_linear, double max_angular) {
        TwistMsg cmd;
        cmd.linear.x = dist_(rng_) * max_linear;
        cmd.angular.z = dist_(rng_) * max_angular;
        return cmd;
    }

    // 生成正弦波速度命令
    TwistMsg generateSinusoidalCommand(double time, double freq = 0.5, double amplitude = 1.0) {
        TwistMsg cmd;
        cmd.linear.x = amplitude * sin(2 * M_PI * freq * time);
        cmd.angular.z = amplitude * 0.5 * cos(2 * M_PI * freq * time);
        return cmd;
    }

    // 生成斜坡速度命令
    TwistMsg generateRampCommand(double time, double slope = 0.5, double max_vel = 2.0) {
        TwistMsg cmd;
        double vel = std::min(slope * time, max_vel);
        cmd.linear.x = vel;
        cmd.angular.z = vel * 0.3;
        return cmd;
    }

private:
    std::mt19937 rng_;
    std::uniform_real_distribution<double> dist_;
};

/**
 * @brief 详细的数据记录器 - 记录和分析速度平滑效果
 */
class VelocityDataRecorder {
public:
    struct VelocityRecord {
        double timestamp;
        TwistMsg input_vel;
        TwistMsg output_vel;
        TwistMsg current_vel;
    };

    VelocityDataRecorder() : start_time_(std::chrono::steady_clock::now()) {}

    void recordData(const TwistMsg& input, const TwistMsg& output, const TwistMsg& current) {
        VelocityRecord record;
        record.timestamp = getCurrentTime();
        record.input_vel = input;
        record.output_vel = output;
        record.current_vel = current;

        records_.push_back(record);
    }

    void printStatistics() const {
        if (records_.empty()) {
            std::cout << "没有记录数据" << std::endl;
            return;
        }

        std::cout << "\n📊 速度平滑统计信息:" << std::endl;
        std::cout << "记录数量: " << records_.size() << std::endl;
        std::cout << "记录时长: " << std::fixed << std::setprecision(2)
                  << (records_.back().timestamp - records_.front().timestamp) << " 秒" << std::endl;

        // 计算平滑效果
        double max_input_accel = 0.0, max_output_accel = 0.0;
        double avg_input_vel = 0.0, avg_output_vel = 0.0;

        for (size_t i = 1; i < records_.size(); ++i) {
            double dt = records_[i].timestamp - records_[i-1].timestamp;
            if (dt > 0.0) {
                // 计算加速度
                double input_accel = std::abs(records_[i].input_vel.linear.x - records_[i-1].input_vel.linear.x) / dt;
                double output_accel = std::abs(records_[i].output_vel.linear.x - records_[i-1].output_vel.linear.x) / dt;

                max_input_accel = std::max(max_input_accel, input_accel);
                max_output_accel = std::max(max_output_accel, output_accel);
            }

            avg_input_vel += std::abs(records_[i].input_vel.linear.x);
            avg_output_vel += std::abs(records_[i].output_vel.linear.x);
        }

        avg_input_vel /= records_.size();
        avg_output_vel /= records_.size();

        std::cout << "最大输入加速度: " << max_input_accel << " m/s²" << std::endl;
        std::cout << "最大输出加速度: " << max_output_accel << " m/s²" << std::endl;
        std::cout << "平均输入速度: " << avg_input_vel << " m/s" << std::endl;
        std::cout << "平均输出速度: " << avg_output_vel << " m/s" << std::endl;
        if (max_input_accel > 0) {
            std::cout << "平滑效果: " << std::setprecision(1)
                      << (1.0 - max_output_accel / max_input_accel) * 100.0 << "%" << std::endl;
        }
    }

    void saveToFile(const std::string& filename) const {
        std::ofstream file(filename);
        if (!file.is_open()) {
            std::cerr << "无法保存数据到文件: " << filename << std::endl;
            return;
        }

        file << "timestamp,input_linear,input_angular,output_linear,output_angular,current_linear,current_angular\n";
        for (const auto& record : records_) {
            file << std::fixed << std::setprecision(6)
                 << record.timestamp << ","
                 << record.input_vel.linear.x << ","
                 << record.input_vel.angular.z << ","
                 << record.output_vel.linear.x << ","
                 << record.output_vel.angular.z << ","
                 << record.current_vel.linear.x << ","
                 << record.current_vel.angular.z << "\n";
        }

        file.close();
        std::cout << "数据已保存到: " << filename << " (共 " << records_.size() << " 条记录)" << std::endl;
    }

    size_t getRecordCount() const {
        return records_.size();
    }

private:
    std::vector<VelocityRecord> records_;
    std::chrono::steady_clock::time_point start_time_;

    double getCurrentTime() const {
        auto now = std::chrono::steady_clock::now();
        auto duration = now - start_time_;
        return std::chrono::duration<double>(duration).count();
    }
};

/**
 * @brief 测试场景管理器
 */
class TestScenarioManager {
public:
    enum ScenarioType {
        STEP_RESPONSE,
        RANDOM_COMMANDS,
        SINUSOIDAL_INPUT,
        RAMP_INPUT,
        EMERGENCY_STOP,
        ACCELERATION_LIMIT_TEST
    };

    static std::string getScenarioName(ScenarioType type) {
        switch (type) {
            case STEP_RESPONSE: return "阶跃响应测试";
            case RANDOM_COMMANDS: return "随机命令测试";
            case SINUSOIDAL_INPUT: return "正弦波输入测试";
            case RAMP_INPUT: return "斜坡输入测试";
            case EMERGENCY_STOP: return "紧急停止测试";
            case ACCELERATION_LIMIT_TEST: return "加速度限制测试";
            default: return "未知测试";
        }
    }

    static void runScenario(VelocitySmootherNoRos& smoother, ScenarioType type,
                           VelocityDataRecorder& recorder, double duration = 10.0) {
        std::cout << "\n🎯 开始执行: " << getScenarioName(type) << std::endl;
        std::cout << "测试时长: " << duration << " 秒" << std::endl;

        VelocityCommandGenerator generator;
        auto start_time = std::chrono::steady_clock::now();

        // 用于同步的变量
        std::mutex output_mutex;
        std::condition_variable output_cv;
        TwistMsg latest_output;
        bool output_received = false;

        // 设置临时回调
        auto original_callback = [&](const std::shared_ptr<TwistMsg>& vel) {
            std::lock_guard<std::mutex> lock(output_mutex);
            latest_output = *vel;
            output_received = true;
            output_cv.notify_one();
        };
        smoother.setSmoothVelPublishCallback(original_callback);

        while (true) {
            auto current_time = std::chrono::steady_clock::now();
            double elapsed = std::chrono::duration<double>(current_time - start_time).count();

            if (elapsed >= duration) break;

            TwistMsg input_cmd;

            switch (type) {
                case STEP_RESPONSE:
                    if (elapsed < 2.0) input_cmd = generator.generateStepCommand(0.0, 0.0);
                    else if (elapsed < 5.0) input_cmd = generator.generateStepCommand(1.5, 0.5);
                    else if (elapsed < 8.0) input_cmd = generator.generateStepCommand(-1.0, -0.8);
                    else input_cmd = generator.generateStepCommand(0.0, 0.0);
                    break;

                case RANDOM_COMMANDS:
                    input_cmd = generator.generateRandomCommand(2.0, 1.5);
                    break;

                case SINUSOIDAL_INPUT:
                    input_cmd = generator.generateSinusoidalCommand(elapsed, 0.3, 1.5);
                    break;

                case RAMP_INPUT:
                    input_cmd = generator.generateRampCommand(elapsed, 0.3, 2.0);
                    break;

                case EMERGENCY_STOP:
                    if (elapsed < 3.0) input_cmd = generator.generateStepCommand(2.0, 1.0);
                    else input_cmd = generator.generateStepCommand(0.0, 0.0);
                    break;

                case ACCELERATION_LIMIT_TEST:
                    if (elapsed < 1.0) input_cmd = generator.generateStepCommand(0.0, 0.0);
                    else if (elapsed < 3.0) input_cmd = generator.generateStepCommand(3.0, 2.0);
                    else if (elapsed < 5.0) input_cmd = generator.generateStepCommand(-2.5, -1.5);
                    else input_cmd = generator.generateStepCommand(0.0, 0.0);
                    break;
            }

            // 输入速度命令
            smoother.inputVelocity(input_cmd);

            // 等待输出
            std::unique_lock<std::mutex> lock(output_mutex);
            output_received = false;
            if (output_cv.wait_for(lock, std::chrono::milliseconds(100), [&] { return output_received; })) {
                recorder.recordData(input_cmd, latest_output, smoother.getCurrentVel());
            } else {
                recorder.recordData(input_cmd, TwistMsg(0.0, 0.0), smoother.getCurrentVel());
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(50)); // 20Hz
        }

        std::cout << "✅ " << getScenarioName(type) << " 完成" << std::endl;
    }
};

/**
 * @brief 简化的数据记录器，专门用于基础测试
 */
class SimpleDataRecorder {
public:
    struct Record {
        double timestamp;
        double input_linear, input_angular;
        double output_linear, output_angular;
        double current_linear, current_angular;
    };
    
    SimpleDataRecorder() : start_time_(std::chrono::steady_clock::now()) {}
    
    void addRecord(double input_linear, double input_angular,
                   double output_linear, double output_angular,
                   double current_linear = 0.0, double current_angular = 0.0) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        Record record;
        record.timestamp = getCurrentTime();
        record.input_linear = input_linear;
        record.input_angular = input_angular;
        record.output_linear = output_linear;
        record.output_angular = output_angular;
        record.current_linear = current_linear;
        record.current_angular = current_angular;
        
        records_.push_back(record);
        
        // 实时打印
        std::cout << std::fixed << std::setprecision(3)
                  << "时间: " << record.timestamp 
                  << "s, 输入: (" << input_linear << ", " << input_angular << ")"
                  << ", 输出: (" << output_linear << ", " << output_angular << ")" << std::endl;
    }
    
    void saveToFile(const std::string& filename) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::ofstream file(filename);
        if (!file.is_open()) {
            std::cerr << "无法保存文件: " << filename << std::endl;
            return;
        }
        
        file << "timestamp,input_linear,input_angular,output_linear,output_angular,current_linear,current_angular\n";
        for (const auto& record : records_) {
            file << std::fixed << std::setprecision(6)
                 << record.timestamp << ","
                 << record.input_linear << ","
                 << record.input_angular << ","
                 << record.output_linear << ","
                 << record.output_angular << ","
                 << record.current_linear << ","
                 << record.current_angular << "\n";
        }
        
        file.close();
        std::cout << "数据已保存到: " << filename << " (共 " << records_.size() << " 条记录)" << std::endl;
    }
    
    size_t getRecordCount() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return records_.size();
    }

private:
    std::vector<Record> records_;
    mutable std::mutex mutex_;
    std::chrono::steady_clock::time_point start_time_;
    
    double getCurrentTime() const {
        auto now = std::chrono::steady_clock::now();
        auto duration = now - start_time_;
        return std::chrono::duration<double>(duration).count();
    }
};

/**
 * @brief 测试速度平滑器的基本功能
 */
void testBasicFunctionality() {
    std::cout << "\n=== 测试基本功能 ===" << std::endl;
    
    VelocitySmootherNoRos smoother("test_smoother");
    SimpleDataRecorder recorder;
    
    // 配置参数
    smoother.setSpeedLimits(2.0, 1.5);
    smoother.setAccelLimits(1.0, 0.8);
    smoother.setDecelFactor(2.0);
    smoother.setFrequency(50.0);
    smoother.setQuiet(false);
    
    // 用于同步的变量
    std::mutex output_mutex;
    std::condition_variable output_cv;
    TwistMsg latest_output;
    bool output_received = false;
    
    // 设置输出回调
    smoother.setSmoothVelPublishCallback([&](const std::shared_ptr<TwistMsg>& vel) {
        std::lock_guard<std::mutex> lock(output_mutex);
        latest_output = *vel;
        output_received = true;
        output_cv.notify_one();
    });
    
    // 初始化并启动
    if (!smoother.init()) {
        std::cerr << "初始化失败" << std::endl;
        return;
    }
    
    smoother.start();
    std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 等待启动
    
    // 测试场景1: 阶跃输入
    std::cout << "\n--- 阶跃输入测试 ---" << std::endl;
    
    struct TestCase {
        double input_linear, input_angular;
        std::string description;
    };
    
    std::vector<TestCase> test_cases = {
        {0.0, 0.0, "零速度"},
        {1.0, 0.0, "正向线性速度"},
        {0.0, 0.5, "正向角速度"},
        {1.5, 0.8, "组合速度"},
        {-1.0, -0.5, "负向速度"},
        {0.0, 0.0, "停止"}
    };
    
    for (const auto& test_case : test_cases) {
        std::cout << "\n测试: " << test_case.description << std::endl;
        
        // 输入速度命令
        TwistMsg input_cmd(test_case.input_linear, test_case.input_angular);
        smoother.inputVelocity(input_cmd);
        
        // 等待并记录多个输出
        for (int i = 0; i < 10; ++i) {
            // 等待输出
            std::unique_lock<std::mutex> lock(output_mutex);
            output_received = false;
            
            // 等待最多100ms
            if (output_cv.wait_for(lock, std::chrono::milliseconds(100), [&] { return output_received; })) {
                // 记录数据
                recorder.addRecord(test_case.input_linear, test_case.input_angular,
                                 latest_output.linear.x, latest_output.angular.z);
            } else {
                std::cout << "警告: 未收到输出" << std::endl;
                recorder.addRecord(test_case.input_linear, test_case.input_angular, 0.0, 0.0);
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
    }
    
    smoother.stop();
    recorder.saveToFile("velocity_smoother_test_basic.csv");
}

/**
 * @brief 测试加速度限制
 */
void testAccelerationLimits() {
    std::cout << "\n=== 测试加速度限制 ===" << std::endl;
    
    VelocitySmootherNoRos smoother("accel_test_smoother");
    SimpleDataRecorder recorder;
    
    // 配置较小的加速度限制以便观察效果
    smoother.setSpeedLimits(3.0, 2.0);
    smoother.setAccelLimits(0.5, 0.4);  // 较小的加速度限制
    smoother.setDecelFactor(2.0);
    smoother.setFrequency(20.0);  // 降低频率以便观察
    smoother.setQuiet(false);
    
    std::mutex output_mutex;
    TwistMsg latest_output;
    bool has_output = false;
    
    smoother.setSmoothVelPublishCallback([&](const std::shared_ptr<TwistMsg>& vel) {
        std::lock_guard<std::mutex> lock(output_mutex);
        latest_output = *vel;
        has_output = true;
    });
    
    smoother.init();
    smoother.start();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 突然给一个大的速度命令
    std::cout << "输入大速度命令: (2.0, 1.5)" << std::endl;
    TwistMsg big_cmd(2.0, 1.5);
    smoother.inputVelocity(big_cmd);
    
    // 记录加速过程
    for (int i = 0; i < 50; ++i) {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        
        std::lock_guard<std::mutex> lock(output_mutex);
        if (has_output) {
            recorder.addRecord(2.0, 1.5, latest_output.linear.x, latest_output.angular.z);
        }
    }
    
    // 突然停止
    std::cout << "输入停止命令: (0.0, 0.0)" << std::endl;
    TwistMsg stop_cmd(0.0, 0.0);
    smoother.inputVelocity(stop_cmd);
    
    // 记录减速过程
    for (int i = 0; i < 30; ++i) {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        
        std::lock_guard<std::mutex> lock(output_mutex);
        if (has_output) {
            recorder.addRecord(0.0, 0.0, latest_output.linear.x, latest_output.angular.z);
        }
    }
    
    smoother.stop();
    recorder.saveToFile("velocity_smoother_test_accel.csv");
}

/**
 * @brief 测试完整的场景
 */
void testCompleteScenarios() {
    std::cout << "\n=== 测试完整场景 ===" << std::endl;

    VelocitySmootherNoRos smoother("scenario_test_smoother");
    VelocityDataRecorder recorder;

    // 配置参数
    smoother.setSpeedLimits(2.0, 1.5);
    smoother.setAccelLimits(1.0, 0.8);
    smoother.setDecelFactor(2.0);
    smoother.setFrequency(50.0);
    smoother.setQuiet(false);

    smoother.init();
    smoother.start();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 定义测试场景
    std::vector<std::pair<TestScenarioManager::ScenarioType, double>> test_scenarios = {
        {TestScenarioManager::STEP_RESPONSE, 8.0},
        {TestScenarioManager::ACCELERATION_LIMIT_TEST, 6.0},
        {TestScenarioManager::SINUSOIDAL_INPUT, 10.0},
        {TestScenarioManager::RANDOM_COMMANDS, 5.0},
        {TestScenarioManager::EMERGENCY_STOP, 5.0}
    };

    // 执行测试场景
    for (const auto& scenario : test_scenarios) {
        TestScenarioManager::runScenario(smoother, scenario.first, recorder, scenario.second);
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

    smoother.stop();
    recorder.printStatistics();
    recorder.saveToFile("velocity_smoother_complete_test.csv");
}

/**
 * @brief 测试参数调优
 */
void testParameterTuning() {
    std::cout << "\n=== 测试参数调优 ===" << std::endl;

    VelocitySmootherNoRos smoother("param_test_smoother");

    smoother.setSpeedLimits(2.0, 1.5);
    smoother.setFrequency(50.0);
    smoother.setQuiet(false);

    smoother.init();
    smoother.start();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    struct ParameterSet {
        double accel_lim_v, accel_lim_w;
        double decel_factor;
        std::string name;
    };

    std::vector<ParameterSet> parameter_sets = {
        {0.5, 0.4, 1.5, "保守参数"},
        {1.5, 1.2, 2.5, "激进参数"},
        {2.0, 1.8, 3.0, "极限参数"}
    };

    for (const auto& params : parameter_sets) {
        std::cout << "\n测试 " << params.name << ":" << std::endl;
        std::cout << "  加速度限制: v=" << params.accel_lim_v << " m/s², w=" << params.accel_lim_w << " rad/s²" << std::endl;
        std::cout << "  减速因子: " << params.decel_factor << std::endl;

        smoother.setAccelLimits(params.accel_lim_v, params.accel_lim_w);
        smoother.setDecelFactor(params.decel_factor);

        // 测试阶跃响应
        VelocityDataRecorder param_recorder;
        TestScenarioManager::runScenario(smoother, TestScenarioManager::STEP_RESPONSE,
                                       param_recorder, 4.0);

        std::cout << "结果:" << std::endl;
        param_recorder.printStatistics();

        std::string filename = "velocity_smoother_" + params.name + "_test.csv";
        param_recorder.saveToFile(filename);
    }

    smoother.stop();
}

/**
 * @brief 性能测试
 */
void testPerformance() {
    std::cout << "\n=== 性能测试 ===" << std::endl;

    VelocitySmootherNoRos smoother("performance_test_smoother");

    smoother.setSpeedLimits(2.0, 1.5);
    smoother.setAccelLimits(1.0, 0.8);
    smoother.setDecelFactor(2.0);
    smoother.setFrequency(100.0);  // 高频率测试
    smoother.setQuiet(true);       // 静默模式

    smoother.init();
    smoother.start();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    auto perf_start = std::chrono::high_resolution_clock::now();
    int iterations = 10000;

    for (int i = 0; i < iterations; ++i) {
        TwistMsg test_cmd(1.0, 0.5);
        smoother.inputVelocity(test_cmd);
        std::this_thread::sleep_for(std::chrono::microseconds(10));
    }

    auto perf_end = std::chrono::high_resolution_clock::now();
    auto perf_duration = std::chrono::duration_cast<std::chrono::microseconds>(perf_end - perf_start);

    std::cout << "处理 " << iterations << " 个速度命令耗时: "
              << perf_duration.count() << " 微秒" << std::endl;
    std::cout << "平均处理时间: " << std::fixed << std::setprecision(2)
              << static_cast<double>(perf_duration.count()) / iterations << " 微秒/命令" << std::endl;
    std::cout << "理论最大频率: " << std::setprecision(0)
              << 1000000.0 / (static_cast<double>(perf_duration.count()) / iterations) << " Hz" << std::endl;

    smoother.stop();
}

/**
 * @brief 主程序
 */
int main() {
    std::cout << "=== VelocitySmootherNoRos 完整测试程序 ===" << std::endl;

    try {
        // 测试基本功能
        testBasicFunctionality();

        // 测试加速度限制
        testAccelerationLimits();

        // 测试完整场景
        testCompleteScenarios();

        // 测试参数调优
        testParameterTuning();

        // 性能测试
        testPerformance();

        std::cout << "\n✅ 所有测试完成" << std::endl;
        std::cout << "\n📁 生成的测试文件:" << std::endl;
        std::cout << "  - velocity_smoother_test_basic.csv" << std::endl;
        std::cout << "  - velocity_smoother_test_accel.csv" << std::endl;
        std::cout << "  - velocity_smoother_complete_test.csv" << std::endl;
        std::cout << "  - velocity_smoother_保守参数_test.csv" << std::endl;
        std::cout << "  - velocity_smoother_激进参数_test.csv" << std::endl;
        std::cout << "  - velocity_smoother_极限参数_test.csv" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "❌ 测试出错: " << e.what() << std::endl;
        return -1;
    }

    return 0;
}
