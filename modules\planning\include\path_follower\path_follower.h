#ifndef PATH_FOLLOWER_H
#define PATH_FOLLOWER_H

#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <mutex>
#include <chrono>
#include <iostream>
#include <fstream>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/common/time.h>
#include <Eigen/Dense>
#include <yaml-cpp/yaml.h>

namespace path_follower {

const double PI = 3.1415926;

/**
 * @brief 时间戳结构
 */
struct TimeStamp {
    double sec;
    
    TimeStamp() : sec(0.0) {}
    TimeStamp(double s) : sec(s) {}
    
    static TimeStamp now() {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        double seconds = std::chrono::duration<double>(duration).count();
        return TimeStamp(seconds);
    }
    
    double toSec() const { return sec; }
};

/**
 * @brief 消息头结构
 */
struct Header {
    TimeStamp stamp;
    std::string frame_id;
    
    Header() : frame_id("") {}
    
    void setCurrentTime() {
        stamp = TimeStamp::now();
    }
};

/**
 * @brief 3D点结构
 */
struct Point {
    double x, y, z;
    
    Point() : x(0.0), y(0.0), z(0.0) {}
    Point(double _x, double _y, double _z) : x(_x), y(_y), z(_z) {}
};

/**
 * @brief 四元数结构
 */
struct Quaternion {
    double x, y, z, w;
    
    Quaternion() : x(0.0), y(0.0), z(0.0), w(1.0) {}
    Quaternion(double _x, double _y, double _z, double _w) : x(_x), y(_y), z(_z), w(_w) {}
    
    // 从欧拉角创建四元数
    static Quaternion fromRPY(double roll, double pitch, double yaw) {
        Quaternion q;
        double cy = cos(yaw * 0.5);
        double sy = sin(yaw * 0.5);
        double cp = cos(pitch * 0.5);
        double sp = sin(pitch * 0.5);
        double cr = cos(roll * 0.5);
        double sr = sin(roll * 0.5);
        
        q.w = cr * cp * cy + sr * sp * sy;
        q.x = sr * cp * cy - cr * sp * sy;
        q.y = cr * sp * cy + sr * cp * sy;
        q.z = cr * cp * sy - sr * sp * cy;
        return q;
    }
    
    // 转换为欧拉角
    void toRPY(double& roll, double& pitch, double& yaw) const {
        // Roll (x-axis rotation)
        double sinr_cosp = 2 * (w * x + y * z);
        double cosr_cosp = 1 - 2 * (x * x + y * y);
        roll = atan2(sinr_cosp, cosr_cosp);
        
        // Pitch (y-axis rotation)
        double sinp = 2 * (w * y - z * x);
        if (abs(sinp) >= 1)
            pitch = copysign(PI / 2, sinp); // use 90 degrees if out of range
        else
            pitch = asin(sinp);
        
        // Yaw (z-axis rotation)
        double siny_cosp = 2 * (w * z + x * y);
        double cosy_cosp = 1 - 2 * (y * y + z * z);
        yaw = atan2(siny_cosp, cosy_cosp);
    }
};

/**
 * @brief 位姿结构
 */
struct Pose {
    Point position;
    Quaternion orientation;
    
    Pose() {}
    Pose(double x, double y, double z, double roll, double pitch, double yaw) 
        : position(x, y, z), orientation(Quaternion::fromRPY(roll, pitch, yaw)) {}
};

/**
 * @brief 带时间戳的位姿
 */
struct PoseStamped {
    Header header;
    Pose pose;
    
    PoseStamped() {}
    PoseStamped(double x, double y, double z, double roll, double pitch, double yaw) 
        : pose(x, y, z, roll, pitch, yaw) {
        header.setCurrentTime();
    }
};

/**
 * @brief 里程计数据结构
 */
struct OdometryData {
    Header header;
    Pose pose;
    struct {
        struct {
            double x, y, z;
        } linear;
        struct {
            double x, y, z;
        } angular;
    } twist;
    
    OdometryData() {
        twist.linear.x = twist.linear.y = twist.linear.z = 0.0;
        twist.angular.x = twist.angular.y = twist.angular.z = 0.0;
    }
    
    OdometryData(double x, double y, double z, double roll, double pitch, double yaw) 
        : pose(x, y, z, roll, pitch, yaw) {
        header.setCurrentTime();
        twist.linear.x = twist.linear.y = twist.linear.z = 0.0;
        twist.angular.x = twist.angular.y = twist.angular.z = 0.0;
    }
};

/**
 * @brief 路径点结构
 */
struct PathPoint {
    PoseStamped pose;
    
    PathPoint() {}
    PathPoint(double x, double y, double z) {
        pose.pose.position.x = x;
        pose.pose.position.y = y;
        pose.pose.position.z = z;
        pose.header.setCurrentTime();
    }
};

/**
 * @brief 路径数据结构
 */
struct PathData {
    Header header;
    std::vector<PathPoint> poses;
    
    PathData() {}
    
    void resize(size_t size) {
        poses.resize(size);
    }
    
    size_t size() const {
        return poses.size();
    }
    
    bool empty() const {
        return poses.empty();
    }
    
    void clear() {
        poses.clear();
    }
};

/**
 * @brief 速度命令结构
 */
struct TwistData {
    struct {
        double x, y, z;
    } linear;
    struct {
        double x, y, z;
    } angular;
    
    TwistData() {
        linear.x = linear.y = linear.z = 0.0;
        angular.x = angular.y = angular.z = 0.0;
    }
    
    TwistData(double vx, double vy, double wz) {
        linear.x = vx;
        linear.y = vy;
        linear.z = 0.0;
        angular.x = 0.0;
        angular.y = 0.0;
        angular.z = wz;
    }
    
    void setZero() {
        linear.x = linear.y = linear.z = 0.0;
        angular.x = angular.y = angular.z = 0.0;
    }
    
    bool isZero() const {
        return (fabs(linear.x) < 1e-6 && fabs(linear.y) < 1e-6 && fabs(linear.z) < 1e-6 &&
                fabs(angular.x) < 1e-6 && fabs(angular.y) < 1e-6 && fabs(angular.z) < 1e-6);
    }
};

/**
 * @brief 布尔消息
 */
struct BoolMsg {
    bool data;
    
    BoolMsg() : data(false) {}
    BoolMsg(bool value) : data(value) {}
};

/**
 * @brief 整数消息
 */
struct Int8Msg {
    int8_t data;
    
    Int8Msg() : data(0) {}
    Int8Msg(int8_t value) : data(value) {}
};

/**
 * @brief 路径跟随器配置参数结构体
 */
struct PathFollowerConfig {
    // 传感器配置
    double sensorOffsetX;
    double sensorOffsetY;
    
    // 发布控制
    int pubSkipNum;
    
    // 驱动模式
    bool twoWayDrive;
    
    // 控制参数
    double lookAheadDis;
    double yawRateGain;
    double stopYawRateGain;
    double maxYawRate;
    double maxSpeed;
    double maxAccel;
    
    // 切换参数
    double switchTimeThre;
    double dirDiffThre;
    double stopDisThre;
    double slowDwnDisThre;
    
    // 倾斜检测参数
    bool useInclRateToSlow;
    double inclRateThre;
    double slowRate1;
    double slowRate2;
    double slowTime1;
    double slowTime2;
    
    bool useInclToStop;
    double inclThre;
    double stopTime;
    
    // 行为参数
    bool noRotAtGoal;
    
    // 默认构造函数
    PathFollowerConfig() {
        // 设置默认值（与原pathFollower.cpp一致）
        sensorOffsetX = 0.0;
        sensorOffsetY = 0.0;
        pubSkipNum = 1;
        twoWayDrive = false;
        lookAheadDis = 0.5;
        yawRateGain = 1.0;
        stopYawRateGain = 1.0;
        maxYawRate = 30.0;
        maxSpeed = 0.8;
        maxAccel = 3.0;
        switchTimeThre = 1.0;
        dirDiffThre = 0.1;
        stopDisThre = 0.2;
        slowDwnDisThre = 1.0;
        useInclRateToSlow = false;
        inclRateThre = 120.0;
        slowRate1 = 0.25;
        slowRate2 = 0.5;
        slowTime1 = 2.0;
        slowTime2 = 2.0;
        useInclToStop = false;
        inclThre = 45.0;
        stopTime = 5.0;
        noRotAtGoal = true;
    }
};

// 回调函数类型定义
using OdometryCallback = std::function<void(const std::shared_ptr<OdometryData>&)>;
using PathCallback = std::function<void(const std::shared_ptr<PathData>&)>;
using BoolCallback = std::function<void(const std::shared_ptr<BoolMsg>&)>;
using Int8Callback = std::function<void(const std::shared_ptr<Int8Msg>&)>;
using TwistCallback = std::function<void(const std::shared_ptr<TwistData>&)>;

/**
 * @brief 去ROS化的路径跟随器类
 */
class PathFollowerNoRos {
public:
    // 构造函数和析构函数
    PathFollowerNoRos(const std::string& config_path = "");
    ~PathFollowerNoRos();

    // 初始化函数
    bool initialize();
    bool loadConfig(const std::string& config_path);

    // 数据输入接口
    void updateOdometry(const OdometryData& odom);
    void updatePath(const PathData& path);
    void updateMode(const BoolMsg& mode);
    void updateGoal(const PoseStamped& goal);
    void updateWebGoal(const PoseStamped& goal);
    void updateStop(const Int8Msg& stop);

    // 输出回调设置
    void setSpeedPublishCallback(TwistCallback callback);

    // 控制接口
    void startFollowing();
    void stopFollowing();
    void pauseFollowing();
    void resumeFollowing();

    // 参数设置
    void setMaxSpeed(double speed);
    void setLookAheadDistance(double distance);
    void setTwoWayDrive(bool enable);
    void setNoRotationAtGoal(bool enable);
    void setYawRateGain(double gain);
    void setMaxYawRate(double rate);
    void setMaxAcceleration(double accel);

    // 状态查询
    bool isInitialized() const;
    bool isRunning() const;
    bool isPaused() const;
    bool isPathValid() const;
    bool hasReachedGoal() const;
    double getDistanceToGoal() const;
    double getCurrentSpeed() const;
    double getCurrentYawRate() const;
    int getCurrentPathPointID() const;
    PathFollowerConfig getConfig() const;

    // 打印状态
    void printStatus();

    // 主循环方法
    void controlLoop();
    void processOnce();

private:
    // 配置参数
    PathFollowerConfig config_;
    std::string config_path_;

    // 运行状态
    bool initialized_;
    bool running_;
    bool paused_;
    std::mutex data_mutex_;

    // 回调函数
    TwistCallback speed_publish_callback_;

    // 原始变量保留 (与原pathFollower.cpp中的全局变量对应)
    bool adjustmode;
    double goalX, goalY, goalZ;
    int nav_start;
    double modifySpeed;
    float joyYaw;
    float vehicleX, vehicleY, vehicleZ;
    float vehicleRoll, vehiclePitch, vehicleYaw;
    float vehicleXRec, vehicleYRec, vehicleZRec;
    float vehicleRollRec, vehiclePitchRec, vehicleYawRec;
    float vehicleYawRate, vehicleSpeed;
    double odomTime;
    double slowInitTime;
    double stopInitTime;
    int pathPointID;
    bool pathInit;
    bool navFwd;
    double switchTime;
    int safetyStop;
    PathData path;
    bool rotinit;
    double odometryTime;
    int pubSkipCount;

    // 私有方法 (保留原始函数功能)
    void odomHandler(const std::shared_ptr<OdometryData>& odom);
    void pathHandler(const std::shared_ptr<PathData>& path);
    void modeHandler(const std::shared_ptr<BoolMsg>& mode);
    void goalHandler(const std::shared_ptr<PoseStamped>& goal);
    void webgoalHandler(const std::shared_ptr<PoseStamped>& goal);
    void stopHandler(const std::shared_ptr<Int8Msg>& stop);

    // 工具方法
    double getCurrentTime();
    void initializeVariables();
    bool loadYamlConfig(const std::string& config_path);

    // 主要控制算法
    void performPathFollowing();
};

} // namespace path_follower

#endif // PATH_FOLLOWER_H
