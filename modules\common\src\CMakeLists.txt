cmake_minimum_required(VERSION 2.8.12)

project(common)

message("Starting to parse CMake file for Common project.")

find_package(PCL REQUIRED)

find_package(PkgConfig REQUIRED)
pkg_check_modules(YAML_CPP REQUIRED yaml-cpp)
# link directory
LINK_DIRECTORIES(${CMAKE_CURRENT_SOURCE_DIR}/../../lib/${PLATFORM_NAME}/${CMAKE_SYSTEM_NAME}/${CONFIG_NAME}/)

# output directory
SET(LIBRARY_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../../lib/${PLATFORM_NAME}/${CMAKE_SYSTEM_NAME}/${CONFIG_NAME}/)

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/.
    "/usr/lib/.",
    "/usr/local/include/.",
    ${PCL_INCLUDE_DIRS}
    ${EIGEN3_INCLUDE_DIR}
    ${YAML_CPP_INCLUDE_DIRS} 
)

include(${CMAKE_CURRENT_SOURCE_DIR}/../cmake/IncludeDirectories_CM.cmake)


# source files
file(GLOB SOURCES_COM
        "./*.cpp"
        "./memory/*.cpp"
        "./thread/*.cpp"
        "./model/*.cpp"
        "./file/*.cpp"
        "./config/*.cpp"
        "./log/*.cpp"
        "./algorithm/*.cpp"
        "./geometry/*.cpp"
        "./platform/*.cpp"

        "./communication/MessagePump/*.cpp"

        )


# header files
file(GLOB_RECURSE HEADERS_COM
    "./*.h"
    "./memory/*.h"
    "./thread/*.h"
    "./model/*.h"
    "./config/*.h"
    "./file/*.h"
    "./log/*.h"
    "./algorithm/*.h"
    "./geometry/*.h"
    "./platform/*.h"
    "./communication/CanProcess/*.h"
    "./communication/CanProcess/peakcan/*.h"
    "./communication/ZeroMQ/*.h"
    "./communication/ZeroMQ/*.inc"
    "./communication/ZeroMQ/Protobuf/*.cc"
    "./communication/MessagePump/*.h"
    "./communication/MessagePump/Parser/*.h"
    "./communication/Serial/*.h"
    "./communication/Udp/*.h"
    )

# link driectory
link_directories(${LIBRARY_OUTPUT_PATH})

add_library(${PROJECT_NAME} STATIC ${HEADERS_COM} ${SOURCES_COM})
target_compile_options(${PROJECT_NAME} PUBLIC -fPIC)
#target_link_libraries(${PROJECT_NAME} ${PCL_LIBRARIES})

add_custom_target(common_Headers SOURCES ${HEADERS_COMMON})
add_custom_target(common_Interfaces SOURCES ${INTERFACES_COMMON})
