#ifndef GLOBAL_TRAJ_GENERATE_H
#define GLOBAL_TRAJ_GENERATE_H

#include <iostream>
#include <stdio.h>
#include <math.h>
#include <unistd.h>
#include <time.h>
#include <fstream>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/range_image/range_image.h>
#include <pcl/filters/filter.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/common/common.h>
#include <pcl/registration/icp.h>

#include <Eigen/Dense>

using namespace std;

// 去ROS化的消息结构体
namespace global_trajec_generate_no_ros {

typedef pcl::PointXYZ PointType;
#define PI 3.14159265

/**
 * @brief 时间戳结构
 */
struct TimeStamp {
    double sec;
    
    TimeStamp() : sec(0.0) {}
    TimeStamp(double s) : sec(s) {}
    
    static TimeStamp now() {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        double seconds = std::chrono::duration<double>(duration).count();
        return TimeStamp(seconds);
    }
    
    double toSec() const { return sec; }
};

/**
 * @brief 消息头结构
 */
struct Header {
    TimeStamp stamp;
    std::string frame_id;
    
    Header() : frame_id("map") {}
    
    void setCurrentTime() {
        stamp = TimeStamp::now();
    }
};

/**
 * @brief 3D点结构
 */
struct Point {
    double x, y, z;
    
    Point() : x(0.0), y(0.0), z(0.0) {}
    Point(double _x, double _y, double _z) : x(_x), y(_y), z(_z) {}
};

/**
 * @brief 四元数结构
 */
struct Quaternion {
    double x, y, z, w;
    
    Quaternion() : x(0.0), y(0.0), z(0.0), w(1.0) {}
    Quaternion(double _x, double _y, double _z, double _w) : x(_x), y(_y), z(_z), w(_w) {}
    
    // 从欧拉角创建四元数
    static Quaternion fromRPY(double roll, double pitch, double yaw) {
        Quaternion q;
        double cy = cos(yaw * 0.5);
        double sy = sin(yaw * 0.5);
        double cp = cos(pitch * 0.5);
        double sp = sin(pitch * 0.5);
        double cr = cos(roll * 0.5);
        double sr = sin(roll * 0.5);

        q.w = cr * cp * cy + sr * sp * sy;
        q.x = sr * cp * cy - cr * sp * sy;
        q.y = cr * sp * cy + sr * cp * sy;
        q.z = cr * cp * sy - sr * sp * cy;
        
        return q;
    }
    
    // 从yaw角创建四元数 (完全保留原有tf::createQuaternionMsgFromYaw功能)
    static Quaternion fromYaw(double yaw) {
        return fromRPY(0.0, 0.0, yaw);
    }
    
    // 转换为欧拉角
    void getRPY(double& roll, double& pitch, double& yaw) const {
        // Roll (x-axis rotation)
        double sinr_cosp = 2 * (w * x + y * z);
        double cosr_cosp = 1 - 2 * (x * x + y * y);
        roll = atan2(sinr_cosp, cosr_cosp);

        // Pitch (y-axis rotation)
        double sinp = 2 * (w * y - z * x);
        if (abs(sinp) >= 1)
            pitch = copysign(M_PI / 2, sinp);
        else
            pitch = asin(sinp);

        // Yaw (z-axis rotation)
        double siny_cosp = 2 * (w * z + x * y);
        double cosy_cosp = 1 - 2 * (y * y + z * z);
        yaw = atan2(siny_cosp, cosy_cosp);
    }
};

/**
 * @brief 位姿结构
 */
struct Pose {
    Point position;
    Quaternion orientation;
    
    Pose() {}
    Pose(double x, double y, double z, double roll, double pitch, double yaw) 
        : position(x, y, z), orientation(Quaternion::fromRPY(roll, pitch, yaw)) {}
};

/**
 * @brief 带时间戳的位姿
 */
struct PoseStamped {
    Header header;
    Pose pose;
    
    PoseStamped() {}
    PoseStamped(double x, double y, double z, double roll, double pitch, double yaw) 
        : pose(x, y, z, roll, pitch, yaw) {
        header.setCurrentTime();
    }
};

/**
 * @brief 里程计消息
 */
struct Odometry {
    Header header;
    std::string child_frame_id;
    struct {
        Pose pose;
        double covariance[36];
    } pose;
    struct {
        struct {
            double x, y, z;
        } linear;
        struct {
            double x, y, z;
        } angular;
        double covariance[36];
    } twist;
    
    Odometry() : child_frame_id("base_link") {
        for (int i = 0; i < 36; ++i) {
            pose.covariance[i] = 0.0;
            twist.covariance[i] = 0.0;
        }
        twist.linear.x = twist.linear.y = twist.linear.z = 0.0;
        twist.angular.x = twist.angular.y = twist.angular.z = 0.0;
    }
};

/**
 * @brief 路径消息
 */
struct Path {
    Header header;
    std::vector<PoseStamped> poses;
    
    Path() {}
};

/**
 * @brief 导航目标消息
 */
struct NavigationTarget {
    int32_t nav_mode;
    int32_t point_id;
    double pose_x, pose_y, pose_z;
    double yaw;
    int32_t point_info;
    int32_t gait;
    int32_t speed;
    int32_t manner;
    int32_t obsmode;
    int32_t navmode;
    
    NavigationTarget() : nav_mode(0), point_id(0), pose_x(0.0), pose_y(0.0), pose_z(0.0),
                        yaw(0.0), point_info(0), gait(0), speed(1), manner(0), obsmode(1), navmode(0) {}
};

/**
 * @brief 导航结果消息
 */
struct NavigationResult {
    int32_t point_id;
    double target_pose_x, target_pose_y, target_pose_z;
    double target_yaw;
    double current_pose_x, current_pose_y, current_pose_z;
    double current_yaw;
    int32_t nav_state;
    
    NavigationResult() : point_id(0), target_pose_x(0.0), target_pose_y(0.0), target_pose_z(0.0),
                        target_yaw(0.0), current_pose_x(0.0), current_pose_y(0.0), current_pose_z(0.0),
                        current_yaw(0.0), nav_state(0) {}
};

/**
 * @brief 轨迹点类 (完全保留原有实现)
 */
class trajPoint {
public:
    float x;
    float y;
    float z;
    float yaw;

    trajPoint(double _x, double _y, double _z, double _yaw) : x(_x), y(_y), z(_z), yaw(_yaw) {}
};

} // namespace global_trajec_generate_no_ros

/**
 * @brief 去ROS化的全局轨迹生成器类
 */
class GlobalTrajecGenerateNoRos {
public:
    GlobalTrajecGenerateNoRos(const std::string& name);
    ~GlobalTrajecGenerateNoRos();
    
    // 初始化和控制
    bool init();
    bool initFromConfig(const std::string& config_file = "");
    void start();
    void stop();
    
    // 参数设置 (完全保留原有参数)
    void setParameters(int indexIncrement, int indexNum);
    void setIndexIncrement(int indexIncrement);
    void setIndexNum(int indexNum);
    
    // 输入函数 (替换ROS订阅)
    void inputOdometry(const global_trajec_generate_no_ros::Odometry& odom);
    void inputGoalPose(const global_trajec_generate_no_ros::PoseStamped& goal);
    void inputWebGoalPose(const global_trajec_generate_no_ros::PoseStamped& goal);
    void inputGlobalPath(const global_trajec_generate_no_ros::Path& path);
    
    // 回调函数设置 (替换ROS发布)
    void setLocalGoalCallback(std::function<void(const std::shared_ptr<global_trajec_generate_no_ros::PoseStamped>&)> callback);
    void setNavigationResultCallback(std::function<void(const std::shared_ptr<global_trajec_generate_no_ros::NavigationResult>&)> callback);
    
    // 状态查询
    bool isInitialized() const;
    bool isRunning() const;
    bool isStartFlag() const;
    bool isLocalGoalInitialized() const;
    
    // 获取当前参数和状态
    int getIndexIncrement() const;
    int getIndexNum() const;
    int getTrajSize() const;
    int getNearestTrajPointIndex() const;
    int getLocalGoalIndex() const;
    global_trajec_generate_no_ros::Point getCurrentGoalPoint() const;
    
    // 配置管理
    bool loadConfiguration(const std::string& config_file);
    bool saveConfiguration(const std::string& config_file) const;
    void printStatus() const;

private:
    // 成员变量
    std::string name_;
    bool initialized_;
    std::atomic<bool> running_;
    std::thread worker_thread_;
    std::mutex data_mutex_;
    
    // 原有的所有变量 (完全保留)
    global_trajec_generate_no_ros::PoseStamped localGoal_;                    // 全局系下的局部目标点
    global_trajec_generate_no_ros::Point posenow_;                           // 终点位置信息
    pcl::PointCloud<global_trajec_generate_no_ros::PointType>::Ptr cloudKeyPoses3DToMap_;
    pcl::KdTreeFLANN<global_trajec_generate_no_ros::PointType>::Ptr kdtreeCloudKeyPoses3DToMap_;
    std::vector<int> pointSearchInd_;
    std::vector<float> pointSearchSqDis_;
    global_trajec_generate_no_ros::Odometry odom_;                           // 载体位置信息
    
    std::vector<global_trajec_generate_no_ros::trajPoint> traj_;
    
    int trajSize_;                                                           // 全局轨迹航迹点数目
    bool localGoalInitialFlag_;                                              // 第一个局部目标点是否初始化
    int indexIncrement_;                                                     // 向前预移动的航迹点索引增量
    int indexNum_;                                                           // 向前预移动的点增量
    int nearestTrajPointIndexToCurrentRobotPos_;                            // 距离当前机器人位置最近的航迹点索引
    int nextTrajPointIndexToCurrentRobotPos_;                               // 距离当前机器人位置最近的航迹点的下一个航迹点索引
    int localGoalIndex_;                                                     // 局部目标点在全局轨迹向量中的索引
    bool start_flag_;                                                        // 是否接收到新的轨迹
    double poseyaw_;                                                         // 目标点航向角
    
    // 回调函数
    std::function<void(const std::shared_ptr<global_trajec_generate_no_ros::PoseStamped>&)> local_goal_callback_;
    std::function<void(const std::shared_ptr<global_trajec_generate_no_ros::NavigationResult>&)> nav_result_callback_;
    
    // 原有的处理函数 (完全保留实现)
    void generateLocalGoal(const std::shared_ptr<const global_trajec_generate_no_ros::Odometry>& msg);
    void generateGlobalGoal(const std::shared_ptr<const global_trajec_generate_no_ros::Path>& msg);
    void goalPoseCallback(const std::shared_ptr<const global_trajec_generate_no_ros::PoseStamped>& msg);
    void webGoalPoseCallback(const std::shared_ptr<const global_trajec_generate_no_ros::PoseStamped>& msg);
    
    // 内部函数
    void controlLoop();
    void setDefaultParameters();
    bool loadConfigurationFromYAML(const std::string& yaml_file);
    bool loadConfigurationFromText(const std::string& config_file);
};

#endif // GLOBAL_TRAJEC_GENERATE_H
