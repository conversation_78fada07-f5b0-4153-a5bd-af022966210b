#include "pid_controller.h"
#include <limits>
#include <iostream>

/**
 * @brief 构造函数
 */
PIDController::PIDController(double kp, double ki, double kd)
    : kp_(kp), ki_(ki), kd_(kd), prev_error_(0.0), integral_(0.0),
      integral_limit_(std::numeric_limits<double>::max()), deadzone_(DEFAULT_DEADZONE)
{
}

/**
 * @brief 默认构造函数
 */
PIDController::PIDController()
    : kp_(0.0), ki_(0.0), kd_(0.0), prev_error_(0.0), integral_(0.0),
      integral_limit_(std::numeric_limits<double>::max()), deadzone_(DEFAULT_DEADZONE)
{
}

/**
 * @brief 析构函数
 */
PIDController::~PIDController()
{
}

/**
 * @brief 设置PID参数 (完全保留原有实现)
 */
void PIDController::Parameter(double Kp, double Ki, double Kd)
{
    kp_ = Kp;
    ki_ = Ki;
    kd_ = Kd;
}

/**
 * @brief 计算PID控制量 (完全保留原有实现)
 * 
 * 这是原有calibration.cpp中的完整实现，包含：
 * - 误差限幅
 * - 积分限幅防止积分饱和
 * - 输出限幅
 * - 死区处理
 */
double PIDController::Control(double error, double dt, double actual, double err_max, double limit)
{
    // 注意：原始实现中dt参数被忽略，固定使用0.01
    // actual参数在原始实现中也未使用
    // 为了完全保留原有功能，这里保持相同的行为
    
#if 0
    // 这是原始代码中被注释掉的简单版本
    double result = 0;
    integral_ += error * DEFAULT_DT;
    double derivative = (error - prev_error_) / DEFAULT_DT;
    prev_error_ = error;
    result = kp_ * error + ki_ * integral_ + kd_ * derivative;
    
    // 对输出控制量大小限幅
    if (result > limit) result = limit;
    else if(result < -limit) result = -limit;
    if(std::abs(result) < deadzone_) result = 0;
    return result;
#else
    // 这是原始代码中实际使用的增强版本
    
    // 误差限幅 (完全保留原有实现)
    error = std::max(std::min(error, err_max), -err_max);
    
    double result = 0;
    
    // 积分计算 (使用固定时间间隔0.01，保持原有行为)
    integral_ += error * DEFAULT_DT;
    
    // 微分计算 (使用固定时间间隔0.01，保持原有行为)
    double derivative = (error - prev_error_) / DEFAULT_DT;
    prev_error_ = error;
    
    // 积分限幅，防止积分饱和 (完全保留原有实现)
    if (ki_ != 0.0) {
        double integral_limit = limit / ki_;
        integral_ = std::max(std::min(integral_, integral_limit), -integral_limit);
    }
    
    // PID计算 (完全保留原有实现)
    result = kp_ * error + ki_ * integral_ + kd_ * derivative;
    
    // 输出限幅 (完全保留原有实现)
    result = std::max(std::min(result, limit), -limit);

    // 死区处理 (完全保留原有实现)
    if(std::abs(result) < deadzone_) {
        result = 0;
    }
    
    return result;
#endif
}

/**
 * @brief 重置PID控制器状态
 */
void PIDController::reset()
{
    prev_error_ = 0.0;
    integral_ = 0.0;
}

/**
 * @brief 获取当前PID参数
 */
void PIDController::getParameters(double& kp, double& ki, double& kd) const
{
    kp = kp_;
    ki = ki_;
    kd = kd_;
}

/**
 * @brief 获取当前积分值
 */
double PIDController::getIntegral() const
{
    return integral_;
}

/**
 * @brief 获取上一次误差值
 */
double PIDController::getPreviousError() const
{
    return prev_error_;
}

/**
 * @brief 设置积分限幅值
 */
void PIDController::setIntegralLimit(double integral_limit)
{
    integral_limit_ = integral_limit;
}

/**
 * @brief 设置死区值
 */
void PIDController::setDeadzone(double deadzone)
{
    deadzone_ = deadzone;
}
