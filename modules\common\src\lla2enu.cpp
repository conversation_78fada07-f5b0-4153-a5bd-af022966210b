#include "lla2enu.h"

#include <GeographicLib/LocalCartesian.hpp>

namespace common_lib{
    namespace lla2enu{

        static GeographicLib::LocalCartesian local_cartesian;

        static double origin_latitude = 0.0;
        static double origin_longitude = 0.0;
        static double origin_altitude = 0.0;

        void GetOrigin(double &latitude, double &longitude, double &altitude)
        {
            latitude = origin_latitude;
            longitude = origin_longitude;
            altitude = origin_altitude;
        }

        void Reset(double latitude, double longitude, double altitude)
        {
            local_cartesian.Reset(latitude, longitude, altitude);

            origin_latitude = latitude;
            origin_longitude = longitude;
            origin_altitude = altitude;
        }

        void Forward(double latitude, double longitude, double altitude,
                     double &local_e, double &local_n, double &local_u)
        {
            local_cartesian.Forward(latitude, longitude, altitude, local_e, local_n,
                                    local_u);
        }

        void Reverse(double local_e, double local_n, double local_u, double &latitude,
                     double &longitude, double &altitude)
        {
            local_cartesian.Reverse(local_e, local_n, local_u, latitude,
                                    longitude, altitude);
        }

    } // namespace common_lib
} // namespace common_lib