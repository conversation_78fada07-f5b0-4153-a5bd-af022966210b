#include "publisher/path_data_publisher_ros1.h"
#include <nav_msgs/Path.h>

#if COMMUNICATION_TYPE == ROS1
namespace communication::ros1 {

PathDataPublisherRos1::PathDataPublisherRos1(ros::NodeHandle &nh, const std::string &topic,
                                             const std::string &frame_id, size_t max_buffer_size)
                                            : PathDataPublisherBase(topic, max_buffer_size), nh_(nh), frame_id_(frame_id) {
    publisher_ = nh_.advertise<nav_msgs::Path>(topic, max_buffer_size);
}

} // namespace communication::ros1

#endif // COMMUNICATION_TYPE == ROS1