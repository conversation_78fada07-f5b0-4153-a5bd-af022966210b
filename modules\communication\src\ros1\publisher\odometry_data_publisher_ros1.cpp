#include "publisher/odometry_data_publisher_ros1.h"
// #include <pcl_conversions/pcl_conversions.h>

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1{

OdometryDataPublisherRos1::OdometryDataPublisherRos1(ros::NodeHandle &nh, const std::string &topic, 
                const std::string &frame_id, const std::string &child_frame_id, size_t max_buffer_size)
            : OdometryPublisherBase(topic, max_buffer_size), nh_(nh), frame_id_(frame_id), child_frame_id_(child_frame_id)
{
    publisher_ = nh_.advertise<nav_msgs::Odometry>(topic, max_buffer_size);
}

}   // namespace communication::ros1{

#endif
    