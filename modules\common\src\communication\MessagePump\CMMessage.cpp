#include "CMMessage.h"
#include "CMTime.h"

CMMessage::CMMessage()
{
    appId = MSG_APP_UNKNOW;
    messageId = MSG_ID_UNKNOW;
    wParam = 0;
    lParam = nullptr;
    time = GetTimestamp();
}

CMMessage& CMMessage::operator=(const CMMessage& rightValue)
{
    if (this == &rightValue)
    {
        return *this;
    }
    else
    {
        this->appId = rightValue.appId;
        this->messageId = rightValue.messageId;
        this->wParam = rightValue.wParam;
        this->lParam = rightValue.lParam;
        this->time = rightValue.time;

        return *this;
    }
}
