#include "CMRWLock.h"

CMRWLock::CMRWLock()
{
    Initialize();
}

CMRWLock::~CMRWLock()
{
    Destroy();
}

cx_int CMRWLock::Initialize()
{
#ifdef WIN32
    InitializeSRWLock(&m_srwLock);
#else
    pthread_rwlock_init(&m_rwlock, NULL);
#endif
    
    return 0;
}

cx_int CMRWLock::Destroy()
{
#ifdef WIN32

#else
    pthread_rwlock_destroy(&m_rwlock);
#endif

    return 0;
}

cx_int CMRWLock::LockR()
{
#ifdef WIN32
    AcquireSRWLockShared(&m_srwLock);
#else
    pthread_rwlock_rdlock(&m_rwlock);
#endif

    return 0;
}

cx_int CMRWLock::UnlockR()
{
#ifdef WIN32
    ReleaseSRWLockShared(&m_srwLock);
#else
    pthread_rwlock_unlock(&m_rwlock);
#endif

    return 0;
}

cx_int CMRWLock::LockW()
{
#ifdef WIN32
    AcquireSRWLockExclusive(&m_srwLock);
#else
    pthread_rwlock_wrlock(&m_rwlock);
#endif

    return 0;
}

cx_int CMRWLock::UnlockW()
{
#ifdef WIN32
    ReleaseSRWLockExclusive(&m_srwLock);
#else
    pthread_rwlock_unlock(&m_rwlock);
#endif

    return 0;
}
