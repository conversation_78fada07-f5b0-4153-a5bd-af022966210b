#ifndef CMMESSAGEPUMP_H
#define CMMESSAGEPUMP_H

#include "CMMessage.h"
#include "CMRWLock.h"

class CMMessagePump
{
public:
    CMMessagePump();
    virtual ~CMMessagePump();

public:
    cx_int SendMessage(const CMMessage& msg);
    cx_int PostMessage(const CMMessage& msg);

    cx_int GetMessage(CMMessage& msg);
    cx_int PeekMessage(CMMessage& msg);

    cx_int GetBackMessage(CMMessage& msg);

    cx_int DestroyMessage(CMMessage& msg);

private:
    CMSharedData<CMMessageDeque> m_deqMessages;
};

CMMessagePump* GetSingleton4MessagePump();

cx_int PostMessage(const CMMessage& msg);
cx_int GetMessage(CMMessage& msg);

#endif // CMMESSAGEPUMP_H
