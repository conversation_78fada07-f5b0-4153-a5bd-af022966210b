#include "CMConfig.h"

using namespace std;


CMConfig::CMConfig( string filename, string delimiter, string comment )
    : m_strDelimiter(delimiter),
      m_strComment(comment)
{
    m_bInit = true;
    // Construct a Config, getting keys and values from given file

    std::ifstream in( filename.c_str() );

    if (!in)
    {
        m_bInit = false;
        File_not_found(filename);
        //if( !in ) throw File_not_found( filename );
    }
    else
    {
        in >> (*this);
        in.close();
    }
}


CMConfig::CMConfig()
    : m_strDelimiter( string(1,'=') ),
      m_strComment( string(1,'#') )
{
    m_bInit = true;
    // Construct a Config without a file; empty
}

bool CMConfig::IsInit()
{
    return m_bInit;
}

bool CMConfig::KeyExists( const string& key ) const
{
    // Indicate whether key is found
    map_const_iterator p = m_strContents.find( key );
    return ( p != m_strContents.end() );
}

/* static */
void CMConfig::Trim( string& inout_s )
{
    // Remove leading and trailing whitespace
    static const char whitespace[] = " \n\t\v\r\f";
    inout_s.erase( 0, inout_s.find_first_not_of(whitespace) );
    inout_s.erase( inout_s.find_last_not_of(whitespace) + 1U );

    return;
}

std::ostream& operator<<( std::ostream& os, const CMConfig& cf )
{
    // Save a Config to os
    for( CMConfig::map_const_iterator p = cf.m_strContents.begin();
        p != cf.m_strContents.end();
        ++p )
    {
        os << p->first << " " << cf.m_strDelimiter << " ";
        os << p->second << std::endl;
    }

    return os;
}

void CMConfig::Remove( const string& key )
{
    // Remove key and its value
    m_strContents.erase( m_strContents.find( key ) );

    return;
}

std::istream& operator>>( std::istream& is, CMConfig& cf )
{
    // Load a Config from is
    // Read in keys and values, keeping internal whitespace
    typedef string::size_type pos;
    const string& delim  = cf.m_strDelimiter;  // separator
    const string& comm   = cf.m_strComment;    // comment
    const pos skip = delim.length();        // length of separator

    string nextline = "";  // might need to read ahead to see where value ends

    while( is || nextline.length() > 0 )
    {
        // Read an entire line at a time
        string line;
        if( nextline.length() > 0 )
        {
            line = nextline;  // we read ahead; use it now
            nextline = "";
        }
        else
        {
            std::getline( is, line );
        }

        // Ignore comments
        line = line.substr( 0, line.find(comm) );

        // Parse the line if it contains a delimiter
        pos delimPos = line.find( delim );
        if( delimPos < string::npos )
        {
            // Extract the key
            string key = line.substr( 0, delimPos );
            line.replace( 0, delimPos+skip, "" );

            // See if value continues on the next line
            // Stop at blank line, next line with a key, end of stream,
            // or end of file sentry
            bool terminate = false;
            while( !terminate && is )
            {
                std::getline( is, nextline );
                terminate = true;

                string nlcopy = nextline;
                CMConfig::Trim(nlcopy);
                if( nlcopy == "" )
                {
                    continue;
                }

                nextline = nextline.substr( 0, nextline.find(comm) );
                if( nextline.find(delim) != string::npos )
                {
                    continue;
                }

                nlcopy = nextline;
                CMConfig::Trim(nlcopy);
                if( nlcopy != "" )
                {
                    line += "\n";
                }

                line += nextline;
                terminate = false;
            }

            // Store key and value
            CMConfig::Trim(key);
            CMConfig::Trim(line);
            cf.m_strContents[key] = line;  // overwrites if key is repeated
        }
    }

    return is;
}

bool CMConfig::FileExist(std::string filename)
{
    bool exist= false;
    std::ifstream in( filename.c_str() );
    if( in )
        exist = true;
    return exist;
}

void CMConfig::ReadFile( string filename, string delimiter,
                      string comment )
{
    m_strDelimiter = delimiter;
    m_strComment = comment;
    std::ifstream in( filename.c_str() );

    if( !in ) throw File_not_found( filename );

    in >> (*this);
}
