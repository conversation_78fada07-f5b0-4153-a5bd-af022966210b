# common/CMakeLists.txt
cmake_minimum_required(VERSION 2.8.3)
project(common)

# 全局设置库文件输出目录
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)

if (NOT DEFINED CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE "Debug")
endif()

if(NOT DEFINED PLATFORM)
  set(PLATFORM "x86_64")
endif()

if(NOT DEFINED COMMUNICATION_TYPE)
  set(COMMUNICATION_TYPE "ROS1")
endif()

set(CMAKE_CXX_STANDARD 14)
#set(CMAKE_CXX_FLAGS "-std=c++17 -march=native")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall")

include_directories(
    include
)

add_library(${PROJECT_NAME}_lib SHARED
  src/common_func.cpp
  src/lla2enu.cpp
)





