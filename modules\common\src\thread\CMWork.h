#ifndef _CM_WORK_H_
#define _CM_WORK_H_

#include "CM_pubhead.h"

#ifdef WIN32
#include "windows.h"
#endif

#ifdef WIN32
typedef void ThreadProc(PTP_CALLBACK_INSTANCE Instance, PVOID Context);
#else
typedef void* ThreadProc(void *);
#endif

class CMWork
{
public:
    CMWork();
    virtual ~CMWork();

#ifdef WIN32
    CMWork(ThreadProc* pfnThreadProc, LPVOID const pContext);
#endif

public:
    cx_int SetName(cx_string strName);
    const cx_string& GetName() const;

public:
    cx_int SetThreadProc(ThreadProc* pfnThreadProc);
    cx_int SetThreadContext(LPVOID pContext);

    ThreadProc* GetThreadProc();
    LPVOID  GetContext();

private:
    cx_int Run();

private:
    ThreadProc* m_pfnThreadProc;
    LPVOID      m_pContext;			        

    cx_string m_strName;

    friend class CMThread;
};

#endif // _CM_WORK_H_
