#include "global_traj_generate.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace global_trajec_generate_no_ros;


/**
 * @brief 全局轨迹生成节点 - 基础节点创建和初始化
 */
int main(int argc, char** argv) {
    std::cout << "=== GlobalTrajecGenerateNoRos 基础节点程序 ===" << std::endl;
    
    try {
        // 创建全局轨迹生成器节点
        GlobalTrajecGenerateNoRos traj_generator("global_trajec_generate_node");
        
        // 设置基本参数
        std::cout << "\n🔧 配置全局轨迹生成器参数..." << std::endl;
        traj_generator.setParameters(
            20,  // indexIncrement - 向前预移动的航迹点索引增量
            10   // indexNum - 向前预移动的点增量
        );
        
        // 设置回调函数
        std::cout << "\n📡 设置回调函数..." << std::endl;
        traj_generator.setLocalGoalCallback([](const std::shared_ptr<PoseStamped>& local_goal) {
            std::cout << "局部目标点发布: " << std::endl;
            std::cout << "  位置: (" << local_goal->pose.position.x << ", " << local_goal->pose.position.y 
                      << ", " << local_goal->pose.position.z << ")" << std::endl;
            
            // 获取朝向角
            double roll, pitch, yaw;
            local_goal->pose.orientation.getRPY(roll, pitch, yaw);
            std::cout << "  朝向: " << yaw * 180.0 / PI << " 度" << std::endl;
            std::cout << "  时间戳: " << local_goal->header.stamp.toSec() << std::endl;
        });
        
        traj_generator.setNavigationResultCallback([](const std::shared_ptr<NavigationResult>& result) {
            std::cout << "导航结果: " << std::endl;
            std::cout << "  点位ID: " << result->point_id << std::endl;
            std::cout << "  导航状态: " << result->nav_state << std::endl;
        });
        
        // 初始化
        std::cout << "\n⚙️ 初始化全局轨迹生成器..." << std::endl;
        if (!traj_generator.init()) {
            std::cerr << "❌ 全局轨迹生成器初始化失败" << std::endl;
            return -1;
        }
        
        // 显示状态
        traj_generator.printStatus();
        
        // 启动全局轨迹生成器
        std::cout << "\n🚀 启动全局轨迹生成器..." << std::endl;
        traj_generator.start();
        
        std::cout << "\n✅ 全局轨迹生成节点启动完成" << std::endl;
        std::cout << "节点状态: " << (traj_generator.isRunning() ? "运行中" : "已停止") << std::endl;
        
        // 保持节点运行
        std::cout << "\n💡 节点正在运行，按Ctrl+C退出..." << std::endl;
        std::cout << "可以通过API输入里程计数据、目标点和全局路径进行测试" << std::endl;
        std::cout << "\n使用说明:" << std::endl;
        std::cout << "1. 首先输入目标点 (inputGoalPose 或 inputWebGoalPose)" << std::endl;
        std::cout << "2. 然后输入全局路径 (inputGlobalPath)" << std::endl;
        std::cout << "3. 最后输入里程计数据 (inputOdometry) 开始生成局部目标点" << std::endl;
        
        while (traj_generator.isRunning()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序执行出错: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
