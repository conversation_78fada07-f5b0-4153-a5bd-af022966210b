#include "ros1/communication_impl_ros1.h"

#include "subscriber/imu_data_subscriber_ros1.h"
#include "subscriber/gnss_data_subscriber_ros1.h"
#include "subscriber/lidar_data_subscriber_ros1.h"
#include "subscriber/odometry_subscriber_ros1.h"
#include "subscriber/simple_data_subscriber_ros1.h"
#include "subscriber/cloud_data_subscriber_ros1.h"
#include "subscriber/path_data_subscriber_ros1.h"
#include "subscriber/pose_data_subscriber_ros1.h"


#include "publisher/simple_data_publisher_ros1.h"
#include "publisher/cloud_data_publisher_ros1.h"
#include "publisher/odometry_data_publisher_ros1.h"
#include "publisher/path_data_publisher_ros1.h"
#include "publisher/pose_data_publisher_ros1.h"


// #include "publisher_base.h"
#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1 {
    // static ros::NodeHandle nh_; // Static NodeHandle for ROS1 communication
    // Constructor implementation for ROS1 communication
    CommunicationRos1Impl::CommunicationRos1Impl(const std::string& module_name) : 
                                            CommunicationImpl(module_name) {
        // Call methods to initialize ROS1 communication and load parameters
        
    //    nh_ = ros::NodeHandle("~"); // Initialize the ROS node handle with a private namespace
    }
   
    CommunicationRos1Impl::~CommunicationRos1Impl()
    {
        // Cleanup if necessary
        // This destructor can be used to release resources or perform any necessary cleanup for ROS1 communication
    }

    bool CommunicationRos1Impl::Initialize(const CommunicationType type) {
        // Initialize ROS1 communication setup
        // This can include setting up subscribers, publishers, and other necessary components for ROS1 communication
        CommunicationImpl::Initialize(type);
        
      //  int argc = 0; // ROS1 requires argc and argv for initialization, but we can set them to 0 and nullptr if not needed
     //   char **argv = nullptr; // ROS1 requires argc and argv for initialization, but we can set them to 0 and nullptr if not needed
      //  ros::init(argc, argv, module_name_); // Initialize the ROS node with a name
        nh_ = ros::NodeHandle("~"); // Initialize the ROS node handle with a private namespace
                
        return true;
    }

    // Start the ROS1 communication thread
    void CommunicationRos1Impl::Run() {
        // This function can be used to start a separate thread for ROS1 communication
        // For example, it can spin the ROS node to process incoming messages
        // This is typically done in a separate thread to avoid blocking the main application
        ros::spin(); // Spin the ROS node to process incoming messages
    }

    std::shared_ptr<ImuDataSubscriberBase> CommunicationRos1Impl::CreateImuDataSubscriber(const std::string& topic) {
        // Create and return a subscriber for IMU data in ROS1
        return std::make_shared<ImuDataSubscriberRos1>(nh_, topic, 100);
    }

    std::shared_ptr<GnssDataSubscriberBase> CommunicationRos1Impl::CreateGnssDataSubscriber(const std::string& topic) {
        // Create and return a subscriber for GNSS data in ROS1
        return std::make_shared<GnssDataSubscriberRos1>(nh_, topic, 10);
    }

    std::shared_ptr<LidarDataSubscriberBase> CommunicationRos1Impl::CreateLidarDataSubscriber(const std::string& topic) {
        // Create and return a subscriber for LiDAR data in ROS1
        return std::make_shared<LidarDataSubscriberRos1>(nh_, topic, 1);
    }

    std::shared_ptr<ImageDataSubscriberBase> CommunicationRos1Impl::CreateImageDataSubscriber(const std::string& topic) {
        // Create and return a subscriber for  camera data in ROS1
       // return std::make_shared<ImageDataSubscriberRos1>(nh_, topic, 10);
        //TODO: Implement camera data subscriber
        return nullptr; // Placeholder for camera data subscriber
    }

    std::shared_ptr<OdometrySubscriberBase> CommunicationRos1Impl::CreateOdometrySubscriber(const std::string& topic) {
        // Create and return a subscriber for odometry data in ROS1
        return std::make_shared<OdometrySubscriberRos1>(nh_, topic, 20);
    }

    std::shared_ptr<IntDataSubscriberBase> CommunicationRos1Impl::CreateIntDataSubscriber(const std::string &topic) {
        // Create and return a subscriber for integer data in ROS1
        return std::make_shared<IntDataSubscriberRos1>(nh_, topic, 100);
    }

    std::shared_ptr<DoubleDataSubscriberBase> CommunicationRos1Impl::CreateDoubleDataSubscriber(const std::string &topic) {
        // Create and return a subscriber for double data in ROS1
        return std::make_shared<DoubleDataSubscriberRos1>(nh_, topic, 100);
    }
    std::shared_ptr<StringDataSubscriberBase> CommunicationRos1Impl::CreateStringDataSubscriber(const std::string &topic) {
        // Create and return a subscriber for string data in ROS1
        return std::make_shared<StringDataSubscriberRos1>(nh_, topic, 100);
    }
    std::shared_ptr<BoolDataSubscriberBase> CommunicationRos1Impl::CreateBoolDataSubscriber(const std::string &topic) {
        // Create and return a subscriber for boolean data in ROS1
        return std::make_shared<BoolDataSubscriberRos1>(nh_, topic, 100);
    }
    std::shared_ptr<CloudDataSubscriberBase> CommunicationRos1Impl::CreateCloudDataSubscriber(const std::string &topic) {
        // Create and return a subscriber for cloud data in ROS1
        return std::make_shared<CloudDataSubscriberRos1>(nh_, topic, 100);
    }

    std::shared_ptr<PathDataSubscriberBase> CommunicationRos1Impl::CreatePathDataSubscriber(const std::string &topic) {
        // Create and return a subscriber for path data in ROS1
        return std::make_shared<PathDataSubscriberRos1>(nh_, topic, 100);
    }

    std::shared_ptr<PoseDataSubscriberBase> CommunicationRos1Impl::CreatePoseDataSubscriber(const std::string &topic) {
        // Create and return a subscriber for pose data in ROS1
        return std::make_shared<PoseDataSubscriberRos1>(nh_, topic, 100);
    }

    std::shared_ptr<CameraIntSubscriberBase> CommunicationRos1Impl::CreateCameraIntSubscriber(const std::string &topic)  {
        // Create and return a  camera intrinsics data subscriber in ROS1
      //  return std::make_shared<CameraIntSubscriberRos1>(nh_, topic, 100);
      //TODO: Implement camera intrinsics data subscriber
      return nullptr; // Placeholder for camera data subscriber
    }

    std::shared_ptr<CameraExtSubscriberBase> CommunicationRos1Impl::CreateCameraExtSubscriber(const std::string &topic)  {
        // Create and return a  camera extrinsics data subscriber in ROS1
      //  return std::make_shared<CameraExtSubscriberRos1>(nh_, topic, 100);
      //TODO: Implement camera extrinsics data subscriber
      return nullptr; // Placeholder for camera data subscriber
    }

    std::shared_ptr<OdometryPublisherBase> CommunicationRos1Impl::CreateOdometryPublisher(const std::string &topic,
                            const std::string &frame_id, const std::string &child_frame_id, size_t max_buffer_size) {
        // Create and return a publisher for odometry data in ROS1
        return std::make_shared<OdometryDataPublisherRos1>(nh_, topic, frame_id, child_frame_id, max_buffer_size);
    }

    std::shared_ptr<IntDataPublisherBase> CommunicationRos1Impl::CreateIntDataPublisher(const std::string &topic, size_t max_buffer_size) {
        // Create and return a publisher for integer data in ROS1
        return std::make_shared<IntDataPublisherRos1>(nh_, topic, max_buffer_size);
    }

    std::shared_ptr<DoubleDataPublisherBase> CommunicationRos1Impl::CreateDoubleDataPublisher(const std::string &topic, size_t max_buffer_size) {
        // Create and return a publisher for double data in ROS1
        return std::make_shared<DoubleDataPublisherRos1>(nh_, topic, max_buffer_size);
    }

    std::shared_ptr<StringDataPublisherBase> CommunicationRos1Impl::CreateStringDataPublisher(const std::string &topic, size_t max_buffer_size) {
        // Create and return a publisher for string data in ROS1
        return std::make_shared<StringDataPublisherRos1>(nh_, topic, max_buffer_size);
    }

    std::shared_ptr<BoolDataPublisherBase> CommunicationRos1Impl::CreateBoolDataPublisher(const std::string &topic, size_t max_buffer_size) {
        // Create and return a publisher for boolean data in ROS1
        return std::make_shared<BoolDataPublisherRos1>(nh_, topic, max_buffer_size);
    }

    std::shared_ptr<CloudDataPublisherBase> CommunicationRos1Impl::CreateCloudDataPublisher(const std::string &topic, 
        const std::string& frame_id, size_t max_buffer_size) {
        // Create and return a publisher for cloud data in ROS1
        return std::make_shared<CloudDataPublisherRos1>(nh_, topic, frame_id, max_buffer_size);
    }

    std::shared_ptr<PathDataPublisherBase> CommunicationRos1Impl::CreatePathDataPublisher(const std::string &topic, 
            const std::string& frame_id, size_t max_buffer_size) {
        // Create and return a publisher for path data in ROS1
        return std::make_shared<PathDataPublisherRos1>(nh_, topic, frame_id, max_buffer_size);
    }

    std::shared_ptr<PoseDataPublisherBase> CommunicationRos1Impl::CreatePoseDataPublisher(const std::string &topic, size_t max_buffer_size) {
        // Create and return a publisher for pose data in ROS1
        return std::make_shared<PoseDataPublisherRos1>(nh_, topic, max_buffer_size);
    }


} // namespace communication::ros1

#endif // COMMUNICATION_TYPE == ROS1