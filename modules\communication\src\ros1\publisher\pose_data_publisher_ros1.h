/**
 * @file pose_data_publisher_ros1.h
 * @brief This file contains the implementation of a ROS1 publisher for pose data.
 * * It inherits from the PoseDataPublisherBase class and provides methods to publish pose data in ROS1 format.
 * * The publisher uses the geometry_msgs::PoseStamped message type to represent pose data.
 * * * The class provides methods to convert pose data to the appropriate ROS message format and publish it on a specified topic.
 * * It also includes methods to get the number of subscribers to the topic.
 */
#pragma once
#include <ros/ros.h>
#include <geometry_msgs/PoseStamped.h>
#include "publisher_base.h"
#include "data_types/pose_data.h"

namespace communication::ros1 {
class PoseDataPublisherRos1 : public PoseDataPublisherBase {
public:
    PoseDataPublisherRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size = 10);

    ~PoseDataPublisherRos1() = default;
protected:
    virtual void PublishMsg() override {
        publisher_.publish(msg_);
    }

    virtual void ToMsg() override {
        msg_.header.stamp = ros::Time(data_.time);
        msg_.header.frame_id = frame_id_;
        msg_.pose.position.x = data_.position[0];
        msg_.pose.position.y = data_.position[1];
        msg_.pose.position.z = data_.position[2];
        msg_.pose.orientation.x = data_.orientation[0];
        msg_.pose.orientation.y = data_.orientation[1];
        msg_.pose.orientation.z = data_.orientation[2];
        msg_.pose.orientation.w = data_.orientation[3];
    }

    virtual int GetSubscriberCount() const override {
        return publisher_.getNumSubscribers();
    }
private:
    ros::NodeHandle &nh_;
    ros::Publisher publisher_;
    geometry_msgs::PoseStamped msg_; // ROS message type for pose data
    std::string frame_id_; // Frame ID for the pose data
};
} // namespace communication::ros1
