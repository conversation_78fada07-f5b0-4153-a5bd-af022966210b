//#include <string.h>

#include "CMThread.h"
#include "CMThreadPool.h"

CMThread::CMThread()
{
    m_pWork = NULL;

    m_TerminateEvent.Create();
    m_FinishedEvent.Create();
    m_RunEvent.Create();

    m_TerminateEvent.Reset();
    m_FinishedEvent.Reset();
    m_RunEvent.Reset();
}

CMThread::~CMThread()
{
    m_TerminateEvent.Destroy();
    m_FinishedEvent.Destroy();
    m_RunEvent.Destroy();
}

cx_int CMThread::AssignWork(CMWork* pWork)
{
    //ASSERT(NULL == m_pWork);

    m_pWork = pWork;

    return 0;
}

cx_int CMThread::Start()
{
    ASSERT(m_pWork);
    if (NULL == m_pWork)
    {
        return -1;
    }

    m_FinishedEvent.Reset();
    m_RunEvent.Reset();

    SetState(CM_TS_RUNNING);
    m_pWork->Run();

    return 0;
}

cx_int CMThread::Pause()
{
    m_RunEvent.Wait(INFINITE);

    return 0;
}

cx_int CMThread::Resume()
{
    m_RunEvent.Set();

    return 0;
}

cx_int CMThread::Stop()
{
    m_TerminateEvent.Set();
    Resume();

#ifdef WIN32
    //(void)WaitForSingleObjectInfinite(m_hThreadHandle, INFINITE); 
#else
    pthread_join(m_pthread, NULL);
#endif

    return 0;
}
           
cx_int CMThread::SetState(CM_THREAD_STATE eState)
{
    m_eState = eState;

    return 0;
}

CM_THREAD_STATE CMThread::GetState()
{
    return m_eState;
}

CMWork* CMThread::GetWork()
{
    return  m_pWork;
}


ThreadProc* CMThread::GetThreadProc()
{
    ASSERT(m_pWork);

    return m_pWork ? m_pWork->GetThreadProc() : NULL;
}

LPVOID  CMThread::GetContext()
{
    ASSERT(m_pWork);

    return m_pWork ? m_pWork->GetContext() : NULL;
}

cx_int CMThread::Create(ThreadProc pfnThreadProc,	LPVOID const pContext)
{
#ifdef LINUX
    pthread_create(&m_pthread, NULL, pfnThreadProc, pContext);
#endif

    return 0;
}

const cx_bool CMThread::IsTerminate()
{
    return m_TerminateEvent.IsTriggered() || GetSingleton4ThreadPool()->IsTerminate();
}

cx_int CMThread::Reset()
{
    //(void)SetPriority(THREAD_PRIORITY_NORMAL);

    m_TerminateEvent.Reset();
    m_FinishedEvent.Reset();
    m_RunEvent.Reset();

    return 0;
}

cx_int CMThread::FinishWork()
{
    m_FinishedEvent.Set();

    return 0;
}

cx_int SleepS(cx_dword dwSeconds)
{
#ifdef WIN32
    Sleep(dwSeconds * 1000);
#else
    sleep(dwSeconds);
#endif // WIN32

    return 0;
}

cx_int SleepMS(cx_dword dwMilliseconds)
{
#ifdef WIN32
    Sleep(dwMilliseconds);
#else
    usleep(dwMilliseconds * 1000);
#endif // WIN32

    return 0;
}

cx_int SleepUS(cx_dword dwMicroseconds)
{
#ifdef WIN32
    Sleep(dwMicroseconds / 1000);
#else
    usleep(dwMicroseconds);
#endif // WIN32

    return 0;
}

cx_int CMThread::GetPriority(int& priority) const
{
    cx_int iResult = 0;

    int policy = 0;
    struct sched_param sp = {0};

    iResult = pthread_getschedparam(m_pthread, &policy, &sp);
    if (0 == iResult)
    {
        priority = sp.sched_priority;
    }

    return iResult;
}

cx_int CMThread::SetPriority(const int iPriority)
{
    cx_int iResult = 0;

    int iPRI = iPriority;

    int policy = 0;
    int priority = 0;
    iResult = GetPriority(priority);

    if (0 != iResult)
    {
        return iResult;
    }

    struct sched_param sp = {0};
    int iMax = sched_get_priority_max(policy);
    int iMin = sched_get_priority_min(policy);

    if (iPRI < iMin)
    {
      iPRI = iMin;
    }

    if (iPRI > iMax)
    {
      iPRI = iMax;
    }

    sp.sched_priority = iPRI;

    iResult = pthread_setschedparam(m_pthread, policy, &sp);

    return iResult;
}
