#ifndef _CM_THREADPOOL_H_
#define _CM_THREADPOOL_H_

#include "CM_pubhead.h"

#include "CMThread.h"
#include "CMWork.h"
#include "CMEvent.h"

#ifdef WIN32
#include "windows.h"
#endif // WIN32

class CMThreadPool
{
public:
    CMThreadPool();
    virtual ~CMThreadPool();

public:
    cx_int Create();
    cx_int Close();

public:
    cx_int SetThreadMinimum(cx_dword dwMin);
    cx_int SetThreadMaximum(cx_dword dwMax);

public:
    CMThread * CreateThread();

#ifdef WIN32

public:
    static VOID NTAPI WorkThread(PTP_CALLBACK_INSTANCE Instance, PVOID Context);

#else
public:
    static void *  WorkThread(void* const pContext);

#endif

public:
    cx_int SubmitThread(CMThread* pThread);

    CMThread* GetIdleThread();
    cx_int ExecuteTask(CMWork * const pWork, CMThread*& pReturnThread);

public:
    cx_bool IsTerminate();
    cx_int DecreateThreads(const cx_dword count);
    cx_bool ThreadFinishWork(CMThread * const pThread);
    cx_int ReleaseThread(CMThread* const pThread);

private:
#ifdef WIN32
    PTP_POOL    m_pThreadPool;
    TP_CALLBACK_ENVIRON    m_cbEnv;
    PTP_CLEANUP_GROUP         m_pCleanupGroup;
#else
#endif // WIN32

    CMThreadSet m_setIdleThreads;
    CMThreadSet m_setAliveThreads;
    CMThreadSet m_setKilledThreads;

    cx_dword    m_dwThreadMinimum;
    cx_dword    m_dwThreadMaximum;

    static cx_bool     g_bInitializedThreadPool;

    CMEvent m_TerminateEvent;
};

CMThreadPool* GetSingleton4ThreadPool();

#endif // _CM_THREADPOOL_H_
