#include "global_traj_generate.h"
#include <iostream>
#include <iomanip>

#ifdef USE_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

using namespace std;
using namespace global_trajec_generate_no_ros;

/**
 * @brief 构造函数
 */
GlobalTrajecGenerateNoRos::GlobalTrajecGenerateNoRos(const std::string& name) 
    : name_(name), initialized_(false), running_(false) {
    setDefaultParameters();
    
    // 初始化PCL相关对象
    cloudKeyPoses3DToMap_.reset(new pcl::PointCloud<PointType>());
    kdtreeCloudKeyPoses3DToMap_.reset(new pcl::KdTreeFLANN<PointType>());
    
    // 设置局部目标点的frame_id
    localGoal_.header.frame_id = "map";
}

/**
 * @brief 析构函数
 */
GlobalTrajecGenerateNoRos::~GlobalTrajecGenerateNoRos() {
    stop();
}

/**
 * @brief 初始化
 */
bool GlobalTrajecGenerateNoRos::init() {
    if (initialized_) {
        std::cout << "GlobalTrajecGenerateNoRos already initialized" << std::endl;
        return true;
    }
    
    initialized_ = true;
    std::cout << "GlobalTrajecGenerateNoRos initialized successfully" << std::endl;
    return true;
}

/**
 * @brief 从配置文件初始化
 */
bool GlobalTrajecGenerateNoRos::initFromConfig(const std::string& config_file) {
    if (!config_file.empty()) {
        if (!loadConfiguration(config_file)) {
            std::cerr << "Failed to load configuration from: " << config_file << std::endl;
            return false;
        }
    }
    return init();
}

/**
 * @brief 启动
 */
void GlobalTrajecGenerateNoRos::start() {
    if (!initialized_) {
        std::cerr << "GlobalTrajecGenerateNoRos not initialized" << std::endl;
        return;
    }
    
    if (running_) {
        std::cout << "GlobalTrajecGenerateNoRos already running" << std::endl;
        return;
    }
    
    running_ = true;
    worker_thread_ = std::thread(&GlobalTrajecGenerateNoRos::controlLoop, this);
    std::cout << "GlobalTrajecGenerateNoRos started" << std::endl;
}

/**
 * @brief 停止
 */
void GlobalTrajecGenerateNoRos::stop() {
    if (running_) {
        running_ = false;
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        std::cout << "GlobalTrajecGenerateNoRos stopped" << std::endl;
    }
}

/**
 * @brief 设置参数 (完全保留原有参数)
 */
void GlobalTrajecGenerateNoRos::setParameters(int indexIncrement, int indexNum) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    indexIncrement_ = indexIncrement;
    indexNum_ = indexNum;
}

void GlobalTrajecGenerateNoRos::setIndexIncrement(int indexIncrement) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    indexIncrement_ = indexIncrement;
}

void GlobalTrajecGenerateNoRos::setIndexNum(int indexNum) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    indexNum_ = indexNum;
}

/**
 * @brief 输入里程计数据 (替换ROS订阅)
 */
void GlobalTrajecGenerateNoRos::inputOdometry(const Odometry& odom) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    odom_ = odom;
    generateLocalGoal(std::make_shared<const Odometry>(odom));
}

/**
 * @brief 输入目标位姿 (替换ROS订阅)
 */
void GlobalTrajecGenerateNoRos::inputGoalPose(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    goalPoseCallback(std::make_shared<const PoseStamped>(goal));
}

/**
 * @brief 输入Web目标位姿 (替换ROS订阅)
 */
void GlobalTrajecGenerateNoRos::inputWebGoalPose(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    webGoalPoseCallback(std::make_shared<const PoseStamped>(goal));
}

/**
 * @brief 输入全局路径 (替换ROS订阅)
 */
void GlobalTrajecGenerateNoRos::inputGlobalPath(const Path& path) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    generateGlobalGoal(std::make_shared<const Path>(path));
}

/**
 * @brief 设置局部目标回调函数 (替换ROS发布)
 */
void GlobalTrajecGenerateNoRos::setLocalGoalCallback(std::function<void(const std::shared_ptr<PoseStamped>&)> callback) {
    local_goal_callback_ = callback;
}

/**
 * @brief 设置导航结果回调函数
 */
void GlobalTrajecGenerateNoRos::setNavigationResultCallback(std::function<void(const std::shared_ptr<NavigationResult>&)> callback) {
    nav_result_callback_ = callback;
}

/**
 * @brief 状态查询函数
 */
bool GlobalTrajecGenerateNoRos::isInitialized() const {
    return initialized_;
}

bool GlobalTrajecGenerateNoRos::isRunning() const {
    return running_;
}

bool GlobalTrajecGenerateNoRos::isStartFlag() const {
    return start_flag_;
}

bool GlobalTrajecGenerateNoRos::isLocalGoalInitialized() const {
    return localGoalInitialFlag_;
}

/**
 * @brief 获取当前参数和状态
 */
int GlobalTrajecGenerateNoRos::getIndexIncrement() const {
    return indexIncrement_;
}

int GlobalTrajecGenerateNoRos::getIndexNum() const {
    return indexNum_;
}

int GlobalTrajecGenerateNoRos::getTrajSize() const {
    return trajSize_;
}

int GlobalTrajecGenerateNoRos::getNearestTrajPointIndex() const {
    return nearestTrajPointIndexToCurrentRobotPos_;
}

int GlobalTrajecGenerateNoRos::getLocalGoalIndex() const {
    return localGoalIndex_;
}

Point GlobalTrajecGenerateNoRos::getCurrentGoalPoint() const {
    if (localGoalIndex_ >= 0 && localGoalIndex_ < static_cast<int>(traj_.size())) {
        return Point(traj_[localGoalIndex_].x, traj_[localGoalIndex_].y, traj_[localGoalIndex_].z);
    }
    return Point();
}

/**
 * @brief 根据当前位置生成局部目标点 (完全保留原有实现)
 */
void GlobalTrajecGenerateNoRos::generateLocalGoal(const std::shared_ptr<const Odometry>& msg) {
    // 若需坐标系转换，修改这里
    PointType pointTmp;
    pointTmp.x = msg->pose.pose.position.x;
    pointTmp.y = msg->pose.pose.position.y;
    pointTmp.z = msg->pose.pose.position.z;
    
    // 局部目标点生成
    if (start_flag_) {
        if (!localGoalInitialFlag_) { // 如果第一个局部目标点未初始化，就在全局轨迹中搜索距离当前位置最邻近的轨迹点
            kdtreeCloudKeyPoses3DToMap_->nearestKSearch(pointTmp, 1, pointSearchInd_, pointSearchSqDis_); // 在全局轨迹向量中搜索距离当前位置最近的轨迹点
            nearestTrajPointIndexToCurrentRobotPos_ = pointSearchInd_[0];
            localGoalInitialFlag_ = true;
        } else {
            nextTrajPointIndexToCurrentRobotPos_ = nearestTrajPointIndexToCurrentRobotPos_ + indexNum_; // 将之后第indexNum个航迹点作为下一个位置航迹点
            if (nextTrajPointIndexToCurrentRobotPos_ > trajSize_ - 1) { // 限制超出轨迹总长度
                nextTrajPointIndexToCurrentRobotPos_ = trajSize_ - 1;
            }
            // 当前机器人位置到当前航迹点距离
            double disCurrentRobotPosToCurrentTrajPoint = std::sqrt(std::pow(pointTmp.x - cloudKeyPoses3DToMap_->points[nearestTrajPointIndexToCurrentRobotPos_].x, 2) +
                                                                   std::pow(pointTmp.y - cloudKeyPoses3DToMap_->points[nearestTrajPointIndexToCurrentRobotPos_].y, 2));
            // 当前机器人位置到下一个航迹点距离
            double disCurrentRobotPosToNextTrajPoint = std::sqrt(std::pow(pointTmp.x - cloudKeyPoses3DToMap_->points[nextTrajPointIndexToCurrentRobotPos_].x, 2) +
                                                                std::pow(pointTmp.y - cloudKeyPoses3DToMap_->points[nextTrajPointIndexToCurrentRobotPos_].y, 2));
            // 如果距离下一个航迹点更近，就把下一个航迹点设置为当前航迹点
            if (disCurrentRobotPosToNextTrajPoint < disCurrentRobotPosToCurrentTrajPoint) {
                nearestTrajPointIndexToCurrentRobotPos_ = nextTrajPointIndexToCurrentRobotPos_;
            }
        }
        // 当前航迹点向后预移动一段距离（加几个索引），作为局部目标点
        localGoalIndex_ = nearestTrajPointIndexToCurrentRobotPos_ + indexIncrement_;
        // 索引不能超过航迹终点的索引
        if (localGoalIndex_ > trajSize_ - 1) {
            localGoalIndex_ = trajSize_ - 1;
            start_flag_ = false; // 该段轨迹引导完成
        }
        // 发布全局系下的局部目标点
        localGoal_.pose.position.x = traj_[localGoalIndex_].x;
        localGoal_.pose.position.y = traj_[localGoalIndex_].y;
        localGoal_.pose.position.z = traj_[localGoalIndex_].z;
        localGoal_.pose.orientation = Quaternion::fromYaw(traj_[localGoalIndex_].yaw);
        localGoal_.header.setCurrentTime();
        
        // 发布局部目标点 (替换ROS发布)
        if (local_goal_callback_) {
            local_goal_callback_(std::make_shared<PoseStamped>(localGoal_));
        }
    }
}

/**
 * @brief 读取全局轨迹并存入kd树中 (完全保留原有实现)
 */
void GlobalTrajecGenerateNoRos::generateGlobalGoal(const std::shared_ptr<const Path>& msg) {
    // 初始化点云和kd树
    localGoalInitialFlag_ = false;
    cloudKeyPoses3DToMap_.reset(new pcl::PointCloud<PointType>());
    kdtreeCloudKeyPoses3DToMap_.reset(new pcl::KdTreeFLANN<PointType>());
    traj_.clear();

    // 读取全局轨迹长度
    size_t cloudSize = msg->poses.size();
    trajSize_ = (int)cloudSize;
    PointType pointTmp;

    // 判断全局轨迹正负向
    if (!msg->poses.empty()) {
        if (pow(posenow_.x - msg->poses[0].pose.position.x, 2) + pow(posenow_.y - msg->poses[0].pose.position.y, 2) >=
            pow(posenow_.x - msg->poses[cloudSize-1].pose.position.x, 2) + pow(posenow_.y - msg->poses[cloudSize-1].pose.position.y, 2)) {
            for (size_t i = 0; i < cloudSize; ++i) {
                pointTmp.x = msg->poses[i].pose.position.x;
                pointTmp.y = msg->poses[i].pose.position.y;
                pointTmp.z = msg->poses[i].pose.position.z;
                cloudKeyPoses3DToMap_->points.push_back(pointTmp);
            }
        } else {
            for (size_t i = 0; i < cloudSize; ++i) {
                pointTmp.x = msg->poses[cloudSize-1-i].pose.position.x;
                pointTmp.y = msg->poses[cloudSize-1-i].pose.position.y;
                pointTmp.z = msg->poses[cloudSize-1-i].pose.position.z;
                cloudKeyPoses3DToMap_->points.push_back(pointTmp);
            }
        }

        // 将非空全局轨迹存入kd树中
        if (!cloudKeyPoses3DToMap_->empty())
            kdtreeCloudKeyPoses3DToMap_->setInputCloud(cloudKeyPoses3DToMap_);

        // 根据关键帧计算航迹点
        trajPoint trajPointTmp1(cloudKeyPoses3DToMap_->points[0].x, cloudKeyPoses3DToMap_->points[0].y, cloudKeyPoses3DToMap_->points[0].z, 0);
        traj_.push_back(trajPointTmp1);
        for (size_t i = 1; i < cloudSize - 1; ++i) {
            // 航向角为当前航迹点指向下一航迹点的方向
            float yawTmp = std::atan2(cloudKeyPoses3DToMap_->points[i].y - cloudKeyPoses3DToMap_->points[i - 1].y,
                                     cloudKeyPoses3DToMap_->points[i].x - cloudKeyPoses3DToMap_->points[i - 1].x);
            trajPoint trajPointTmp2(cloudKeyPoses3DToMap_->points[i].x, cloudKeyPoses3DToMap_->points[i].y, cloudKeyPoses3DToMap_->points[i].z, yawTmp);
            traj_.push_back(trajPointTmp2);
        }
        trajPoint trajPointTmp3(cloudKeyPoses3DToMap_->points[cloudSize - 1].x, cloudKeyPoses3DToMap_->points[cloudSize - 1].y,
                               cloudKeyPoses3DToMap_->points[cloudSize - 1].z, poseyaw_);
        traj_.push_back(trajPointTmp3);
        traj_[0].yaw = traj_[1].yaw;

        // 新的轨迹成功读取
        start_flag_ = true;

        std::cout << "全局轨迹已更新，轨迹点数量: " << trajSize_ << std::endl;
    }
}

/**
 * @brief 读取终点信息 (完全保留原有实现)
 */
void GlobalTrajecGenerateNoRos::goalPoseCallback(const std::shared_ptr<const PoseStamped>& msg) {
    posenow_.x = msg->pose.position.x;
    posenow_.y = msg->pose.position.y;
    // posenow_.z = msg->pose.position.z;

    // 四元数转欧拉角 (完全保留原有逻辑)
    Quaternion quat = msg->pose.orientation;
    double roll, pitch, yaw; // 定义存储r\p\y的容器
    quat.getRPY(roll, pitch, yaw); // 进行转换
    poseyaw_ = yaw;

    std::cout << "目标点已设置: (" << posenow_.x << ", " << posenow_.y << "), 朝向: " << poseyaw_ * 180.0 / PI << " 度" << std::endl;
}

/**
 * @brief 读取Web终点信息 (完全保留原有实现)
 */
void GlobalTrajecGenerateNoRos::webGoalPoseCallback(const std::shared_ptr<const PoseStamped>& msg) {
    posenow_.x = msg->pose.position.x;
    posenow_.y = msg->pose.position.y;
    // goal.pose.position.z = target->pose.position.z;
    // goal.pose.orientation = tf::createQuaternionMsgFromYaw(target->yaw);
    poseyaw_ = 0; // 是不是要改成 msg->pose.orientation.z * M_PI / 180.0;
    // poseyaw_ = msg->pose.orientation.z * M_PI / 180.0;

    std::cout << "Web目标点已设置: (" << posenow_.x << ", " << posenow_.y << "), 朝向: " << poseyaw_ * 180.0 / PI << " 度" << std::endl;
}

/**
 * @brief 控制循环 (替换原有的ROS主循环)
 */
void GlobalTrajecGenerateNoRos::controlLoop() {
    const double loop_rate = 100.0; // 100Hz，保持与原有频率一致
    const auto sleep_duration = std::chrono::duration<double>(1.0 / loop_rate);

    while (running_) {
        // 保持运行状态，等待输入
        std::this_thread::sleep_for(sleep_duration);
    }
}

/**
 * @brief 设置默认参数 (完全保留原有默认值)
 */
void GlobalTrajecGenerateNoRos::setDefaultParameters() {
    indexIncrement_ = 20;                                    // 原有默认值
    indexNum_ = 10;                                          // 原有默认值
    trajSize_ = 0;
    localGoalInitialFlag_ = false;
    nearestTrajPointIndexToCurrentRobotPos_ = 0;
    nextTrajPointIndexToCurrentRobotPos_ = 0;
    localGoalIndex_ = 0;
    start_flag_ = false;
    poseyaw_ = 0.0;
}

/**
 * @brief 加载配置文件
 */
bool GlobalTrajecGenerateNoRos::loadConfiguration(const std::string& config_file) {
    if (config_file.empty()) {
        std::cout << "No config file specified, using default parameters" << std::endl;
        return true;
    }

    // 根据文件扩展名选择加载方式
    if (config_file.find(".yaml") != std::string::npos ||
        config_file.find(".yml") != std::string::npos) {
        return loadConfigurationFromYAML(config_file);
    } else {
        return loadConfigurationFromText(config_file);
    }
}

/**
 * @brief 从YAML文件加载配置
 */
bool GlobalTrajecGenerateNoRos::loadConfigurationFromYAML(const std::string& yaml_file) {
#ifdef USE_YAML_CPP
    try {
        YAML::Node config = YAML::LoadFile(yaml_file);

        if (config["indexIncrement"]) indexIncrement_ = config["indexIncrement"].as<int>();
        if (config["indexNum"]) indexNum_ = config["indexNum"].as<int>();

        std::cout << "Configuration loaded from YAML: " << yaml_file << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error loading YAML config: " << e.what() << std::endl;
        return false;
    }
#else
    std::cerr << "YAML support not compiled. Please install yaml-cpp and recompile." << std::endl;
    return false;
#endif
}

/**
 * @brief 从文本文件加载配置
 */
bool GlobalTrajecGenerateNoRos::loadConfigurationFromText(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Cannot open config file: " << config_file << std::endl;
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        // 跳过注释和空行
        if (line.empty() || line[0] == '#') continue;

        size_t pos = line.find('=');
        if (pos != std::string::npos) {
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);

            // 去除空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            // 设置参数
            if (key == "indexIncrement") indexIncrement_ = std::stoi(value);
            else if (key == "indexNum") indexNum_ = std::stoi(value);
        }
    }

    file.close();
    std::cout << "Configuration loaded from text file: " << config_file << std::endl;
    return true;
}

/**
 * @brief 保存配置到文件
 */
bool GlobalTrajecGenerateNoRos::saveConfiguration(const std::string& config_file) const {
    std::ofstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Cannot create config file: " << config_file << std::endl;
        return false;
    }

    file << "# GlobalTrajecGenerate Configuration File" << std::endl;
    file << "# Generated automatically" << std::endl;
    file << std::endl;
    file << "indexIncrement = " << indexIncrement_ << std::endl;
    file << "indexNum = " << indexNum_ << std::endl;

    file.close();
    std::cout << "Configuration saved to: " << config_file << std::endl;
    return true;
}

/**
 * @brief 打印当前状态
 */
void GlobalTrajecGenerateNoRos::printStatus() const {
    std::cout << "\n=== GlobalTrajecGenerateNoRos Status ===" << std::endl;
    std::cout << "Name: " << name_ << std::endl;
    std::cout << "Initialized: " << (initialized_ ? "Yes" : "No") << std::endl;
    std::cout << "Running: " << (running_ ? "Yes" : "No") << std::endl;
    std::cout << "Start Flag: " << (start_flag_ ? "Yes" : "No") << std::endl;
    std::cout << "Local Goal Initialized: " << (localGoalInitialFlag_ ? "Yes" : "No") << std::endl;
    std::cout << std::endl;
    std::cout << "Parameters:" << std::endl;
    std::cout << "  indexIncrement: " << indexIncrement_ << std::endl;
    std::cout << "  indexNum: " << indexNum_ << std::endl;
    std::cout << std::endl;
    std::cout << "Trajectory Status:" << std::endl;
    std::cout << "  trajSize: " << trajSize_ << std::endl;
    std::cout << "  nearestTrajPointIndex: " << nearestTrajPointIndexToCurrentRobotPos_ << std::endl;
    std::cout << "  localGoalIndex: " << localGoalIndex_ << std::endl;
    std::cout << std::endl;
    std::cout << "Goal Position:" << std::endl;
    std::cout << "  posenow: (" << posenow_.x << ", " << posenow_.y << ", " << posenow_.z << ")" << std::endl;
    std::cout << "  poseyaw: " << poseyaw_ * 180.0 / PI << " degrees" << std::endl;
    std::cout << "=========================================" << std::endl;
}
