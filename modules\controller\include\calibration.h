#pragma once

#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include <iomanip>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#ifdef NO_EIGEN3
    #include "calibration_stub.h"
#else
    #include <Eigen/Dense>
#endif

#ifdef NO_PCL
    #include "calibration_stub.h"
#else
    #include <pcl/point_cloud.h>
    #include <pcl/point_types.h>
    #include <pcl/filters/voxel_grid.h>
    #include <pcl/kdtree/kdtree_flann.h>
    #include <pcl/common/time.h>
    #include <pcl/registration/icp.h>
    #include <pcl/io/pcd_io.h>
#endif

using namespace std;

#define pi 3.1415926535

// 前向声明
class PIDController;

// 去ROS化的消息结构体
namespace calibration_no_ros {

/**
 * @brief 时间戳结构
 */
struct TimeStamp {
    double sec;
    
    TimeStamp() : sec(0.0) {}
    TimeStamp(double s) : sec(s) {}
    
    static TimeStamp now() {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        double seconds = std::chrono::duration<double>(duration).count();
        return TimeStamp(seconds);
    }
    
    double toSec() const { return sec; }
};

/**
 * @brief 消息头结构
 */
struct Header {
    TimeStamp stamp;
    std::string frame_id;
    
    Header() : frame_id("") {}
    
    void setCurrentTime() {
        stamp = TimeStamp::now();
    }
};

/**
 * @brief 3D点结构
 */
struct Point {
    double x, y, z;
    
    Point() : x(0.0), y(0.0), z(0.0) {}
    Point(double _x, double _y, double _z) : x(_x), y(_y), z(_z) {}
};

/**
 * @brief 四元数结构
 */
struct Quaternion {
    double x, y, z, w;
    
    Quaternion() : x(0.0), y(0.0), z(0.0), w(1.0) {}
    Quaternion(double _x, double _y, double _z, double _w) : x(_x), y(_y), z(_z), w(_w) {}
    
    // 从欧拉角创建四元数
    static Quaternion fromYaw(double yaw) {
        Quaternion q;
        q.w = cos(yaw * 0.5);
        q.x = 0.0;
        q.y = 0.0;
        q.z = sin(yaw * 0.5);
        return q;
    }
    
    // 转换为欧拉角
    double toYaw() const {
        return atan2(2.0 * (w * z + x * y), 1.0 - 2.0 * (y * y + z * z));
    }
};

/**
 * @brief 位姿结构
 */
struct Pose {
    Point position;
    Quaternion orientation;
    
    Pose() {}
    Pose(double x, double y, double z, double yaw) 
        : position(x, y, z), orientation(Quaternion::fromYaw(yaw)) {}
};

/**
 * @brief 带协方差的位姿
 */
struct PoseWithCovariance {
    Pose pose;
    std::vector<double> covariance;  // 6x6协方差矩阵
    
    PoseWithCovariance() : covariance(36, 0.0) {}
};

/**
 * @brief 3D速度结构
 */
struct Vector3 {
    double x, y, z;
    
    Vector3() : x(0.0), y(0.0), z(0.0) {}
    Vector3(double _x, double _y, double _z) : x(_x), y(_y), z(_z) {}
};

/**
 * @brief 扭矩结构（线速度和角速度）
 */
struct Twist {
    double linear_x = 0.0;
    double linear_y = 0.0;
    double angular_z = 0.0;
};

/**
 * @brief 带协方差的扭矩
 */
struct TwistWithCovariance {
    Twist twist;
    std::vector<double> covariance;  // 6x6协方差矩阵
    
    TwistWithCovariance() : covariance(36, 0.0) {}
};

/**
 * @brief 里程计消息
 */
struct Odometry {
    struct Header {
        double toSec() const { return 0.0; }
    } header;
    struct Pose {
        struct Position {
            double x = 0.0;
            double y = 0.0;
            double z = 0.0;
        } position;
        struct Orientation {
            double x = 0.0;
            double y = 0.0;
            double z = 0.0;
            double w = 1.0;
            double toYaw() const { return 0.0; }
        } orientation;
    } pose;
};

/**
 * @brief 带时间戳的位姿
 */
struct PoseStamped {
    struct Pose {
        struct Position {
            double x = 0.0;
            double y = 0.0;
            double z = 0.0;
        } position;
        struct Orientation {
            double x = 0.0;
            double y = 0.0;
            double z = 0.0;
            double w = 1.0;
            double toYaw() const { return 0.0; }
        } orientation;
    } pose;
};

/**
 * @brief 布尔消息
 */
struct BoolMsg {
    bool data = false;
};

/**
 * @brief 整数消息
 */
struct Int8Msg {
    int8_t data = 0;
};

/**
 * @brief 点云消息
 */
struct PointCloud2 {
    std::vector<uint8_t> data;
};

/**
 * @brief 导航目标消息
 */
struct NavigationTarget {
    int32_t nav_mode;
    int32_t point_id;
    double pose_x, pose_y, pose_z;
    double yaw;
    int32_t point_info;
    int32_t gait;
    int32_t speed;
    int32_t manner;
    int32_t obsmode;
    int32_t navmode;
    
    NavigationTarget() : nav_mode(0), point_id(0), pose_x(0.0), pose_y(0.0), pose_z(0.0),
                        yaw(0.0), point_info(0), gait(0), speed(0), manner(0), obsmode(0), navmode(0) {}
};

/**
 * @brief 导航结果消息
 */
struct NavigationResult {
    int32_t point_id;
    double target_pose_x, target_pose_y, target_pose_z;
    double target_yaw;
    double current_pose_x, current_pose_y, current_pose_z;
    double current_yaw;
    int32_t nav_state;
    
    NavigationResult() : point_id(0), target_pose_x(0.0), target_pose_y(0.0), target_pose_z(0.0),
                        target_yaw(0.0), current_pose_x(0.0), current_pose_y(0.0), current_pose_z(0.0),
                        current_yaw(0.0), nav_state(0) {}
};

/**
 * @brief 发布者模板
 */
template<typename T>
class Publisher {
public:
    Publisher() {}
    
    void publish(const T& msg) {
        if (callback_) {
            auto msg_ptr = std::make_shared<T>(msg);
            callback_(msg_ptr);
        }
    }
    
    void setCallback(std::function<void(const std::shared_ptr<T>&)> callback) {
        callback_ = callback;
    }

private:
    std::function<void(const std::shared_ptr<T>&)> callback_;
};

/**
 * @brief 订阅者模板
 */
template<typename T>
class Subscriber {
public:
    Subscriber() {}
    
    void setCallback(std::function<void(const std::shared_ptr<const T>&)> callback) {
        callback_ = callback;
    }
    
    void inputMessage(const T& msg) {
        if (callback_) {
            auto msg_ptr = std::make_shared<const T>(msg);
            callback_(msg_ptr);
        }
    }

private:
    std::function<void(const std::shared_ptr<const T>&)> callback_;
};

/**
 * @brief 去ROS化的校准控制器类
 */
class CalibrationNoRos {
public:
    CalibrationNoRos(const std::string& name);
    ~CalibrationNoRos();
    
    // 初始化
    bool init();
    
    // 启动
    void start();
    
    // 停止
    void stop();
    
    // 参数设置函数
    void setPIDParameters(double p_yaw, double i_yaw, double d_yaw,
                         double p_x, double i_x, double d_x,
                         double p_y, double i_y, double d_y);
    
    void setErrorLimits(double yaw_max, double yaw_min, double x_max, double y_max);
    void setVelocityLimits(double x_max, double y_max, double yaw_max);
    void setPrecision(double yaw_precision, double x_precision, double y_precision);
    
    // 输入函数
    void inputOdometry(const Odometry& odom);
    void inputGoal(const PoseStamped& goal);
    void inputWebGoal(const PoseStamped& goal);
    void inputMode(const BoolMsg& mode);
    void inputTerrainCloud(const PointCloud2& cloud);
    
    // 回调函数设置
    void setSpeedPublishCallback(std::function<void(const std::shared_ptr<Twist>&)> callback);
    void setStopPublishCallback(std::function<void(const std::shared_ptr<Int8Msg>&)> callback);
    void setInnerStopPublishCallback(std::function<void(const std::shared_ptr<Int8Msg>&)> callback);
    void setModePublishCallback(std::function<void(const std::shared_ptr<BoolMsg>&)> callback);
    
    // 状态查询
    bool isInitialized() const;
    bool isRunning() const;
    bool hasArrived() const;
    
    // 获取当前状态
    double getCurrentX() const;
    double getCurrentY() const;
    double getCurrentZ() const;
    double getCurrentYaw() const;
    
    double getGoalX() const;
    double getGoalY() const;
    double getGoalZ() const;
    double getGoalYaw() const;
    
    void printStatus() const;

private:
    // 成员变量
    std::string name_;
    bool initialized_;
    std::atomic<bool> running_;
    std::thread worker_thread_;
    std::mutex data_mutex_;
    
    // 回调函数
    std::function<void(const std::shared_ptr<Twist>&)> speed_callback_;
    std::function<void(const std::shared_ptr<Int8Msg>&)> stop_callback_;
    std::function<void(const std::shared_ptr<Int8Msg>&)> inner_stop_callback_;
    std::function<void(const std::shared_ptr<BoolMsg>&)> mode_callback_;
    
    // 原有的所有变量
    float vehicleX = 0, vehicleY = 0, vehicleZ = 0;
    float vehicleRoll = 0, vehiclePitch = 0, vehicleYaw = 0;
    double odom_time = 0;
    double goalX = 0, goalY = 0, goalZ = 0, goalYaw = 0;
    int nav_start = 0, id = 0;
    double p_vel_yaw, i_vel_yaw, d_vel_yaw;
    double p_vel_x, i_vel_x, d_vel_x;
    double p_vel_y, i_vel_y, d_vel_y;
    double errorYaw_max, errorX_max, errorY_max, errorYaw_min, set_yaw_precision, set_x_precision, set_y_precision;
    double use_errorYaw;
    double last_angular_z = 0, last_linear_x = 0, last_linear_y = 0;
    double Yaw_max, X_max, Y_max;
    BoolMsg adjustmode;
    BoolMsg posecalibration;
    double start_time, end_time, start_time_point;
    bool newTerrainCloud = false;
    double vehicleLength = 1.0;
    double vehicleWidth = 0.5;
    double odometryTime = 0;
    int arrived = 1;
    
    pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloud;
    pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloudCrop;
    
    // PID控制器（使用全局PIDController类）
    std::unique_ptr<::PIDController> pid_yaw;
    std::unique_ptr<::PIDController> pid_x;
    std::unique_ptr<::PIDController> pid_y;

    // 私有方法
    void initializeVariables();
    void setDefaultParameters();
    Eigen::Vector3d Quat2rpy(const Eigen::Quaterniond& quat);
    double normalizeAngle(double angle);
    void odomHandler(const std::shared_ptr<const Odometry>& odomIn);
    void goalHandler(const std::shared_ptr<const PoseStamped>& goal);
    void webgoalHandler(const std::shared_ptr<const PoseStamped>& goal);
    void modeHandler(const std::shared_ptr<const BoolMsg>& mode);
    void terrainCloudHandler(const std::shared_ptr<const PointCloud2>& terrainCloud2);
    void controlLoop();
};

} // namespace calibration_no_ros
