#include "calibration.h"
#include "pid_controller.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace std;

// 辅助函数：获取当前时间（秒）
inline double getCurrentTimeSeconds() {
    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration<double>(duration).count();
}

namespace calibration_no_ros {

// CalibrationNoRos类的简化实现
CalibrationNoRos::CalibrationNoRos(const std::string& name)
    : name_(name), initialized_(false), running_(false) {
    initializeVariables();
}

CalibrationNoRos::~CalibrationNoRos() {
    stop();
}

bool CalibrationNoRos::init() {
    if (initialized_) {
        std::cout << "CalibrationNoRos already initialized" << std::endl;
        return true;
    }
    
    setDefaultParameters();
    
    // 初始化PID控制器（使用全局PIDController类）
    pid_yaw = std::make_unique<::PIDController>(p_vel_yaw, i_vel_yaw, d_vel_yaw);
    pid_x = std::make_unique<::PIDController>(p_vel_x, i_vel_x, d_vel_x);
    pid_y = std::make_unique<::PIDController>(p_vel_y, i_vel_y, d_vel_y);
    
    initialized_ = true;
    std::cout << "CalibrationNoRos initialized successfully" << std::endl;
    return true;
}

void CalibrationNoRos::start() {
    if (!initialized_) {
        std::cerr << "CalibrationNoRos not initialized" << std::endl;
        return;
    }
    
    if (running_) {
        std::cout << "CalibrationNoRos already running" << std::endl;
        return;
    }
    
    running_ = true;
    worker_thread_ = std::thread(&CalibrationNoRos::controlLoop, this);
    std::cout << "CalibrationNoRos started" << std::endl;
}

void CalibrationNoRos::stop() {
    if (running_) {
        running_ = false;
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        std::cout << "CalibrationNoRos stopped" << std::endl;
    }
}

void CalibrationNoRos::setPIDParameters(double p_yaw, double i_yaw, double d_yaw,
                                       double p_x, double i_x, double d_x,
                                       double p_y, double i_y, double d_y) {
    p_vel_yaw = p_yaw; i_vel_yaw = i_yaw; d_vel_yaw = d_yaw;
    p_vel_x = p_x; i_vel_x = i_x; d_vel_x = d_x;
    p_vel_y = p_y; i_vel_y = i_y; d_vel_y = d_y;
    
    if (initialized_) {
        pid_x->Parameter(p_vel_x, i_vel_x, d_vel_x);
        pid_y->Parameter(p_vel_y, i_vel_y, d_vel_y);
        pid_yaw->Parameter(p_vel_yaw, i_vel_yaw, d_vel_yaw);
    }
}

void CalibrationNoRos::setErrorLimits(double yaw_max, double yaw_min, double x_max, double y_max) {
    errorYaw_max = yaw_max; errorYaw_min = yaw_min;
    errorX_max = x_max; errorY_max = y_max;
}

void CalibrationNoRos::setVelocityLimits(double x_max, double y_max, double yaw_max) {
    X_max = x_max; Y_max = y_max; Yaw_max = yaw_max;
}

void CalibrationNoRos::setPrecision(double yaw_precision, double x_precision, double y_precision) {
    set_yaw_precision = yaw_precision;
    set_x_precision = x_precision;
    set_y_precision = y_precision;
}

void CalibrationNoRos::inputOdometry(const Odometry& odom) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    odomHandler(std::make_shared<const Odometry>(odom));
}

void CalibrationNoRos::inputGoal(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    goalHandler(std::make_shared<const PoseStamped>(goal));
}

void CalibrationNoRos::inputWebGoal(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    webgoalHandler(std::make_shared<const PoseStamped>(goal));
}

void CalibrationNoRos::inputMode(const BoolMsg& mode) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    modeHandler(std::make_shared<const BoolMsg>(mode));
}

void CalibrationNoRos::inputTerrainCloud(const PointCloud2& cloud) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    terrainCloudHandler(std::make_shared<const PointCloud2>(cloud));
}

void CalibrationNoRos::setSpeedPublishCallback(std::function<void(const std::shared_ptr<Twist>&)> callback) {
    speed_callback_ = callback;
}

void CalibrationNoRos::setStopPublishCallback(std::function<void(const std::shared_ptr<Int8Msg>&)> callback) {
    stop_callback_ = callback;
}

void CalibrationNoRos::setInnerStopPublishCallback(std::function<void(const std::shared_ptr<Int8Msg>&)> callback) {
    inner_stop_callback_ = callback;
}

void CalibrationNoRos::setModePublishCallback(std::function<void(const std::shared_ptr<BoolMsg>&)> callback) {
    mode_callback_ = callback;
}

bool CalibrationNoRos::isInitialized() const { return initialized_; }
bool CalibrationNoRos::isRunning() const { return running_; }
bool CalibrationNoRos::hasArrived() const { return arrived == 1; }

double CalibrationNoRos::getCurrentX() const { return vehicleX; }
double CalibrationNoRos::getCurrentY() const { return vehicleY; }
double CalibrationNoRos::getCurrentZ() const { return vehicleZ; }
double CalibrationNoRos::getCurrentYaw() const { return vehicleYaw; }

double CalibrationNoRos::getGoalX() const { return goalX; }
double CalibrationNoRos::getGoalY() const { return goalY; }
double CalibrationNoRos::getGoalZ() const { return goalZ; }
double CalibrationNoRos::getGoalYaw() const { return goalYaw; }

void CalibrationNoRos::printStatus() const {
    std::cout << "\n=== CalibrationNoRos Status ===" << std::endl;
    std::cout << "Name: " << name_ << std::endl;
    std::cout << "Initialized: " << (initialized_ ? "Yes" : "No") << std::endl;
    std::cout << "Running: " << (running_ ? "Yes" : "No") << std::endl;
    std::cout << "Navigation Started: " << (nav_start ? "Yes" : "No") << std::endl;
    std::cout << "Arrived: " << (arrived ? "Yes" : "No") << std::endl;
    std::cout << "Adjust Mode: " << (adjustmode.data ? "Yes" : "No") << std::endl;
    std::cout << "Current Position: (" << vehicleX << ", " << vehicleY << ", " << vehicleZ << ")" << std::endl;
    std::cout << "Current Orientation: " << vehicleYaw * 57.3 << " deg" << std::endl;
    std::cout << "Goal Position: (" << goalX << ", " << goalY << ", " << goalZ << ")" << std::endl;
    std::cout << "Goal Orientation: " << goalYaw * 57.3 << " deg" << std::endl;
    std::cout << "===============================" << std::endl;
}

// 私有方法实现
void CalibrationNoRos::initializeVariables() {
    terrainCloud.reset(new pcl::PointCloud<pcl::PointXYZI>());
    terrainCloudCrop.reset(new pcl::PointCloud<pcl::PointXYZI>());
}

void CalibrationNoRos::setDefaultParameters() {
    p_vel_yaw = 0.5; i_vel_yaw = 0.1; d_vel_yaw = 0.0;
    p_vel_x = 0.5; i_vel_x = 0.1; d_vel_x = 0.0;
    p_vel_y = 0.5; i_vel_y = 0.1; d_vel_y = 0.0;
    errorYaw_max = 3.0; errorYaw_min = 1.0;
    errorX_max = 3.0; errorY_max = 3.0;
    X_max = 0.5; Y_max = 0.1; Yaw_max = 0.1;
    set_yaw_precision = 0.3;
    set_x_precision = 0.3;
    set_y_precision = 0.3;
}

Eigen::Vector3d CalibrationNoRos::Quat2rpy(const Eigen::Quaterniond& quat) {
    Eigen::Vector3d atti;
    // 简化的四元数到欧拉角转换
    double w = quat.w(), x = quat.x(), y = quat.y(), z = quat.z();
    double yaw = atan2(2.0 * (w * z + x * y), 1.0 - 2.0 * (y * y + z * z));
    double pitch = asin(2.0 * (w * y - z * x));
    double roll = atan2(2.0 * (w * x + y * z), 1.0 - 2.0 * (x * x + y * y));
    
    atti[0] = yaw;
    atti[1] = pitch;
    atti[2] = roll;
    return atti;
}

double CalibrationNoRos::normalizeAngle(double angle) {
    while (angle > M_PI) {
        angle -= 2. * M_PI;
    }
    while (angle < -M_PI) {
        angle += 2. * M_PI;
    }
    return angle;
}

void CalibrationNoRos::odomHandler(const std::shared_ptr<const Odometry>& odomIn) {
    // 简化的里程计处理
    vehicleX = odomIn->pose.position.x;
    vehicleY = odomIn->pose.position.y;
    vehicleZ = odomIn->pose.position.z;
    vehicleYaw = odomIn->pose.orientation.toYaw();
    odom_time = odomIn->header.toSec();
    odometryTime = getCurrentTimeSeconds();
}

void CalibrationNoRos::goalHandler(const std::shared_ptr<const PoseStamped>& goal) {
    goalX = goal->pose.position.x;
    goalY = goal->pose.position.y;
    goalYaw = goal->pose.orientation.toYaw();
    nav_start = true;
    arrived = 0;
}

void CalibrationNoRos::webgoalHandler(const std::shared_ptr<const PoseStamped>& goal) {
    goalX = goal->pose.position.x;
    goalY = goal->pose.position.y;
    goalYaw = (goal->pose.orientation.z * M_PI) / 180;
    nav_start = true;
    arrived = 0;
}

void CalibrationNoRos::modeHandler(const std::shared_ptr<const BoolMsg>& mode) {
    adjustmode.data = mode->data;
    start_time_point = getCurrentTimeSeconds();
}

void CalibrationNoRos::terrainCloudHandler(const std::shared_ptr<const PointCloud2>& terrainCloud2) {
    terrainCloud->clear();
    terrainCloudCrop->clear();
    
    // 简化的点云处理
    pcl::PointXYZI point;
    for (size_t i = 0; i < terrainCloud2->data.size() / 16; i++) {
        point.x = 0.0; point.y = 0.0; point.z = 0.0; point.intensity = 0.5;
        if (point.intensity > 0.1) {
            terrainCloudCrop->push_back(point);
        }
    }
    newTerrainCloud = true;
}

void CalibrationNoRos::controlLoop() {
    const double loop_rate = 10.0; // 10Hz (降低频率以减少输出)
    const auto sleep_duration = std::chrono::duration<double>(1.0 / loop_rate);

    while (running_) {
        std::lock_guard<std::mutex> lock(data_mutex_);

        if (adjustmode.data && nav_start == 1 && newTerrainCloud) {
            newTerrainCloud = false;
            Twist cmd_vel;

            // 简化的控制逻辑
            double errorX = goalX - vehicleX;
            double errorY = goalY - vehicleY;
            double errorYaw = goalYaw - vehicleYaw;

            // 角度归一化
            errorYaw = normalizeAngle(errorYaw);

            // 简单的控制逻辑
            if (abs(errorX) > set_x_precision || abs(errorY) > set_y_precision) {
                cmd_vel.linear_x = errorX * 0.5;
                cmd_vel.linear_y = errorY * 0.5;
            } else if (abs(errorYaw) > set_yaw_precision) {
                cmd_vel.angular_z = errorYaw * 0.5;
            } else {
                // 到达目标
                arrived = 1;
                adjustmode.data = false;
                std::cout << "*** 已到达目标点 ***" << std::endl;
            }

            // 发布速度命令
            if (speed_callback_) {
                speed_callback_(std::make_shared<Twist>(cmd_vel));
            }
        }

        std::this_thread::sleep_for(sleep_duration);
    }
}

} // namespace calibration_no_ros
