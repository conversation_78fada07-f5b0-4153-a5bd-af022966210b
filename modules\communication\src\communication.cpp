#include "communication.h"
#include <typeinfo>

#if COMMUNICATION_TYPE == ROS1
// #include <ros/ros.h>
#include "communication_impl_ros1.h"
#endif

namespace communication{

Communication::Communication(const std::string& module_name)
                            : module_name_(module_name), 
                            communication_impl_(nullptr){
 
}

Communication::~Communication(){
	
}

bool Communication::Initialize(const std::string& config_file_path) {
    //Load the configuration file path
    //TODO: Add logic to load the configuration file and set up parameters
    type_ = CommunicationType::ROS1; // Default to ROS1 for now



    #if COMMUNICATION_TYPE == ROS1
        // Create the ROS1 communication implementation
        int argc = 0; // ROS1 requires argc and argv for initialization, but we can set them to 0 and nullptr if not needed
        char **argv = nullptr; // ROS1 requires argc and argv for initialization, but we can set them to 0 and nullptr if not needed
        ros::init(argc, argv, module_name_); // Initialize the ROS node with a name   

        communication_impl_ = std::make_shared<communication::ros1::CommunicationRos1Impl>(module_name_);
    #elif COMMUNICATION_TYPE == ROS2
        // Create the ROS2 communication implementation
        // communication_impl_ = std::make_shared<communication::ros2::CommunicationRos2Impl>(module_name_);
    #elif COMMUNICATION_TYPE == TCP
        // Create the TCP communication implementation
        // communication_impl_ = std::make_shared<communication::tcp::CommunicationTcpImpl>(module_name_);
    #else
        // Handle unsupported communication types
        throw std::runtime_error("Unsupported communication type specified.");
    #endif
    

    // Initialize the communication implementation
    if (communication_impl_) {
        return communication_impl_->Initialize(type_);
    } else {
        return false; // Failed to initialize communication implementation
    }
}

// Create subscribers for IMU, GNSS, and LiDAR data
std::shared_ptr<ImuDataSubscriberBase> Communication::CreateImuDataSubscriber(const std::string& imu_topic){
    // Create and return an IMU data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateImuDataSubscriber(imu_topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
  
}

std::shared_ptr<GnssDataSubscriberBase> Communication::CreateGnssDataSubscriber(const std::string& gnss_topic){
    // Create and return a GNSS data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateGnssDataSubscriber(gnss_topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<LidarDataSubscriberBase> Communication::CreateLidarDataSubscriber(const std::string& lidar_topic){
    // Create and return a LiDAR data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateLidarDataSubscriber(lidar_topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<OdometrySubscriberBase> Communication::CreateOdometrySubscriber(const std::string& topic){
    // Create and return an odometry subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateOdometrySubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<IntDataSubscriberBase> Communication::CreateIntDataSubscriber(const std::string &topic) {
    // Create and return an integer data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateIntDataSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<DoubleDataSubscriberBase> Communication::CreateDoubleDataSubscriber(const std::string &topic) {
    // Create and return a double data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateDoubleDataSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<StringDataSubscriberBase> Communication::CreateStringDataSubscriber(const std::string &topic) {
    // Create and return a string data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateStringDataSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<BoolDataSubscriberBase> Communication::CreateBoolDataSubscriber(const std::string &topic) {
    // Create and return a boolean data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateBoolDataSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<CloudDataSubscriberBase> Communication::CreateCloudDataSubscriber(const std::string &topic) {
    // Create and return a cloud data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateCloudDataSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<PathDataSubscriberBase> Communication::CreatePathDataSubscriber(const std::string &topic) {
    // Create and return a path data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreatePathDataSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<PoseDataSubscriberBase> Communication::CreatePoseDataSubscriber(const std::string &topic) {
    // Create and return a pose data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreatePoseDataSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<CameraIntSubscriberBase> Communication::CreateCameraIntSubscriber(const std::string &topic) {
    // Create and return a  camera intrinsics data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateCameraIntSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}
std::shared_ptr<CameraExtSubscriberBase> Communication::CreateCameraExtSubscriber(const std::string &topic) {
    // Create and return a  camera extrinsics data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateCameraExtSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<ImageDataSubscriberBase> Communication::CreateImageDataSubscriber(const std::string &topic) {
    // Create and return a  camera data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateImageDataSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<OdometryPublisherBase> Communication::CreateOdometryPublisher(const std::string &topic, 
    const std::string& frame_id, const std::string& child_frame_id, size_t max_buffer_size) {
    // Create and return an odometry publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateOdometryPublisher(topic, frame_id, child_frame_id, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<IntDataPublisherBase> Communication::CreateIntDataPublisher(const std::string &topic, size_t max_buffer_size) {
    // Create and return an integer data publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateIntDataPublisher(topic, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<DoubleDataPublisherBase> Communication::CreateDoubleDataPublisher(const std::string &topic, size_t max_buffer_size) {
    // Create and return a double data publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateDoubleDataPublisher(topic, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<StringDataPublisherBase> Communication::CreateStringDataPublisher(const std::string &topic, size_t max_buffer_size) {
    // Create and return a string data publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateStringDataPublisher(topic, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<BoolDataPublisherBase> Communication::CreateBoolDataPublisher(const std::string &topic, size_t max_buffer_size) {
    // Create and return a boolean data publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateBoolDataPublisher(topic, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}


std::shared_ptr<CloudDataPublisherBase> Communication::CreateCloudDataPublisher(const std::string &topic, 
    const std::string& frame_id, size_t max_buffer_size) {
    // Create and return a cloud data publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateCloudDataPublisher(topic, frame_id, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<PathDataPublisherBase> Communication::CreatePathDataPublisher(const std::string &topic,
    const std::string& frame_id, size_t max_buffer_size) {
    // Create and return a path data publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreatePathDataPublisher(topic, frame_id, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<TwistDataPublisherBase> Communication::CreateTwistDataPublisher(const std::string &topic,
    const std::string& frame_id, size_t max_buffer_size) {
    // Create and return a twist data publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateTwistDataPublisher(topic, frame_id, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<PoseDataPublisherBase> Communication::CreatePoseDataPublisher(const std::string &topic, size_t max_buffer_size) {
    // Create and return a pose data publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreatePoseDataPublisher(topic, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}


// Run the communication implementation
void Communication::Run() {
    // Start the communication implementation
    // This can include starting threads, initializing subscribers, publishers, etc.
    if (communication_impl_) {
        communication_impl_->Run(); // Call the Run method of the communication implementation
    } else {
        // Handle the case where communication_impl_ is not initialized
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}


} //namespace communication {