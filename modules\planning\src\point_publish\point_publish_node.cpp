#include "point_publish.cpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace point_publish_no_ros;

/**
 * @brief 点发布节点 - 基础节点创建和初始化
 */
int main(int argc, char** argv) {
    std::cout << "=== PointPublishNoRos 基础节点程序 ===" << std::endl;
    
    try {
        // 创建点发布器节点
        PointPublishNoRos point_publisher("point_publish_node");
        
        // 设置基本参数
        std::cout << "\n🔧 配置点发布器参数..." << std::endl;
        point_publisher.setParameters(
            0,  // point_id
            0,  // point_info
            0,  // gait
            1,  // speed
            0,  // manner
            1,  // obsmode
            0   // navmode
        );
        
        // 设置回调函数
        std::cout << "\n📡 设置回调函数..." << std::endl;
        point_publisher.setNavigationTargetCallback([](const std::shared_ptr<NavigationTarget>& target) {
            std::cout << "导航目标发布: " << std::endl;
            std::cout << "  位置: (" << target->pose_x << ", " << target->pose_y 
                      << ", " << target->pose_z << ")" << std::endl;
            std::cout << "  朝向: " << target->yaw * 57.3 << " 度" << std::endl;
            std::cout << "  点位ID: " << target->point_id << std::endl;
            std::cout << "  步态: " << target->gait << std::endl;
            std::cout << "  速度: " << target->speed << std::endl;
            std::cout << "  导航模式: " << target->navmode << std::endl;
        });
        
        // 初始化
        std::cout << "\n⚙️ 初始化点发布器..." << std::endl;
        if (!point_publisher.init()) {
            std::cerr << "❌ 点发布器初始化失败" << std::endl;
            return -1;
        }
        
        // 显示状态
        point_publisher.printStatus();
        
        // 启动点发布器
        std::cout << "\n🚀 启动点发布器..." << std::endl;
        point_publisher.start();
        
        std::cout << "\n✅ 点发布节点启动完成" << std::endl;
        std::cout << "节点状态: " << (point_publisher.isRunning() ? "运行中" : "已停止") << std::endl;
        
        // 保持节点运行
        std::cout << "\n💡 节点正在运行，按Ctrl+C退出..." << std::endl;
        std::cout << "可以通过API输入目标点进行测试" << std::endl;
        
        while (point_publisher.isRunning()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序执行出错: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
