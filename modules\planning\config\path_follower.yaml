# PathFollowerNoRos 配置文件
# 去ROS化路径跟随器参数配置

# 传感器配置
sensor_config:
  offset_x: 0.0                    # 传感器X方向偏移 (m)
  offset_y: 0.0                    # 传感器Y方向偏移 (m)

# 控制参数配置
control_config:
  look_ahead_distance: 0.5         # 前视距离 (m)
  yaw_rate_gain: 1.0               # 角速度增益
  stop_yaw_rate_gain: 1.0          # 停止时角速度增益
  max_yaw_rate: 30.0               # 最大角速度 (deg/s)
  max_speed: 0.8                   # 最大线速度 (m/s)
  max_acceleration: 3.0            # 最大加速度 (m/s²)

# 切换参数配置
switching_config:
  switch_time_threshold: 1.0       # 切换时间阈值 (s)
  direction_diff_threshold: 0.1    # 方向差异阈值 (rad)
  stop_distance_threshold: 0.2     # 停止距离阈值 (m)
  slow_down_distance_threshold: 1.0 # 减速距离阈值 (m)

# 倾斜检测配置
inclination_config:
  use_inclination_rate_to_slow: false  # 是否使用倾斜率减速
  inclination_rate_threshold: 120.0    # 倾斜率阈值 (deg/s)
  slow_rate_1: 0.25                    # 第一级减速比例
  slow_rate_2: 0.5                     # 第二级减速比例
  slow_time_1: 2.0                     # 第一级减速时间 (s)
  slow_time_2: 2.0                     # 第二级减速时间 (s)
  
  use_inclination_to_stop: false       # 是否使用倾斜停止
  inclination_threshold: 45.0          # 倾斜阈值 (deg)
  stop_time: 5.0                       # 停止时间 (s)

# 行为参数配置
behavior_config:
  no_rotation_at_goal: true        # 到达目标时是否禁止旋转
  two_way_drive: false             # 是否启用双向驱动
  publish_skip_number: 1           # 发布跳过数量

# 运行时参数配置
runtime_config:
  control_frequency: 100           # 控制频率 (Hz)
  timeout_threshold: 0.5           # 超时阈值 (s)

# 调试参数配置
debug_config:
  enable_debug: false              # 是否启用调试
  print_status: false              # 是否打印状态
  debug_output_path: "/tmp/path_follower_debug" # 调试输出路径

# 速度模式配置
speed_modes:
  normal:
    max_speed: 0.8                 # 普通模式最大速度 (m/s)
    max_acceleration: 3.0          # 普通模式最大加速度 (m/s²)
  
  slow:
    max_speed: 0.5                 # 慢速模式最大速度 (m/s)
    max_acceleration: 2.0          # 慢速模式最大加速度 (m/s²)
  
  fast:
    max_speed: 1.2                 # 快速模式最大速度 (m/s)
    max_acceleration: 4.0          # 快速模式最大加速度 (m/s²)

# 安全参数配置
safety_config:
  emergency_stop_deceleration: 5.0 # 紧急停止减速度 (m/s²)
  collision_avoidance_distance: 0.3 # 碰撞避免距离 (m)
  max_lateral_acceleration: 2.0    # 最大横向加速度 (m/s²)
  
  # 安全检查
  enable_safety_checks: true       # 是否启用安全检查
  check_path_validity: true        # 是否检查路径有效性
  check_goal_reachability: true    # 是否检查目标可达性

# 路径跟随算法配置
algorithm_config:
  # Pure Pursuit算法参数
  pure_pursuit:
    enable: true                   # 是否启用Pure Pursuit算法
    min_lookahead_distance: 0.3    # 最小前视距离 (m)
    max_lookahead_distance: 2.0    # 最大前视距离 (m)
    lookahead_ratio: 0.1           # 前视距离比例
  
  # Stanley算法参数
  stanley:
    enable: false                  # 是否启用Stanley算法
    cross_track_gain: 1.0          # 横向误差增益
    heading_gain: 1.0              # 航向误差增益
  
  # 路径平滑参数
  path_smoothing:
    enable: false                  # 是否启用路径平滑
    smoothing_factor: 0.1          # 平滑因子
    max_smoothing_iterations: 10   # 最大平滑迭代次数

# 车辆模型配置
vehicle_model:
  # 车辆几何参数
  wheelbase: 0.5                   # 轴距 (m)
  track_width: 0.4                 # 轮距 (m)
  vehicle_length: 0.6              # 车辆长度 (m)
  vehicle_width: 0.4               # 车辆宽度 (m)
  
  # 运动学参数
  min_turning_radius: 0.3          # 最小转弯半径 (m)
  max_steering_angle: 30.0         # 最大转向角 (deg)
  
  # 动力学参数
  max_linear_velocity: 1.0         # 最大线速度 (m/s)
  max_angular_velocity: 2.0        # 最大角速度 (rad/s)

# 环境适应配置
environment_config:
  # 室内环境
  indoor:
    max_speed: 0.5                 # 室内最大速度 (m/s)
    look_ahead_distance: 0.3       # 室内前视距离 (m)
    yaw_rate_gain: 1.5             # 室内角速度增益
  
  # 户外环境
  outdoor:
    max_speed: 1.0                 # 户外最大速度 (m/s)
    look_ahead_distance: 0.8       # 户外前视距离 (m)
    yaw_rate_gain: 0.8             # 户外角速度增益
  
  # 狭窄通道
  narrow_passage:
    max_speed: 0.3                 # 狭窄通道最大速度 (m/s)
    look_ahead_distance: 0.2       # 狭窄通道前视距离 (m)
    direction_diff_threshold: 0.05 # 狭窄通道方向差异阈值 (rad)

# 性能优化配置
performance_config:
  # 计算优化
  enable_fast_math: false          # 是否启用快速数学运算
  use_lookup_tables: false         # 是否使用查找表
  
  # 内存优化
  preallocate_buffers: true        # 是否预分配缓冲区
  buffer_size: 1000                # 缓冲区大小
  
  # 线程优化
  use_separate_thread: true        # 是否使用独立线程
  thread_priority: 0               # 线程优先级

# 日志配置
logging_config:
  log_level: "INFO"                # 日志级别: DEBUG, INFO, WARN, ERROR
  log_to_file: false               # 是否记录到文件
  log_file_path: "/tmp/path_follower.log" # 日志文件路径
  max_log_file_size: 10            # 最大日志文件大小 (MB)
  log_rotation: true               # 是否启用日志轮转
  
  # 特定日志开关
  log_velocity_commands: false     # 是否记录速度命令
  log_path_updates: false          # 是否记录路径更新
  log_goal_updates: true           # 是否记录目标更新
  log_safety_events: true          # 是否记录安全事件

# 测试和仿真配置
simulation_config:
  enable_simulation_mode: false    # 是否启用仿真模式
  simulation_time_scale: 1.0       # 仿真时间缩放
  add_noise: false                 # 是否添加噪声
  noise_level: 0.01                # 噪声级别
  
  # 仿真环境
  virtual_obstacles: []            # 虚拟障碍物列表
  test_scenarios: []               # 测试场景列表

# 高级配置
advanced_config:
  # 内存管理
  memory_management:
    enable_memory_pool: false      # 是否启用内存池
    pool_size: 1024                # 内存池大小 (KB)
    enable_garbage_collection: true # 是否启用垃圾回收
  
  # 多线程
  threading:
    enable_multithreading: false   # 是否启用多线程
    num_threads: 2                 # 线程数量
    thread_priority: 0             # 线程优先级
  
  # 优化选项
  optimization:
    enable_compiler_optimizations: true # 是否启用编译器优化
    use_vectorization: false       # 是否使用向量化
    cache_optimization: true       # 是否启用缓存优化

# 兼容性配置
compatibility_config:
  # 与原pathFollower.cpp的兼容性设置
  original_pathfollower_mode: true # 是否启用原始pathFollower兼容模式
  preserve_original_behavior: true # 是否保持原始行为
  
  # 参数映射
  parameter_mapping:
    # 将原始参数名映射到新的配置结构
    sensorOffsetX: sensor_config.offset_x
    sensorOffsetY: sensor_config.offset_y
    lookAheadDis: control_config.look_ahead_distance
    yawRateGain: control_config.yaw_rate_gain
    stopYawRateGain: control_config.stop_yaw_rate_gain
    maxYawRate: control_config.max_yaw_rate
    maxSpeed: control_config.max_speed
    maxAccel: control_config.max_acceleration
    switchTimeThre: switching_config.switch_time_threshold
    dirDiffThre: switching_config.direction_diff_threshold
    stopDisThre: switching_config.stop_distance_threshold
    slowDwnDisThre: switching_config.slow_down_distance_threshold
    useInclRateToSlow: inclination_config.use_inclination_rate_to_slow
    inclRateThre: inclination_config.inclination_rate_threshold
    slowRate1: inclination_config.slow_rate_1
    slowRate2: inclination_config.slow_rate_2
    slowTime1: inclination_config.slow_time_1
    slowTime2: inclination_config.slow_time_2
    useInclToStop: inclination_config.use_inclination_to_stop
    inclThre: inclination_config.inclination_threshold
    stopTime: inclination_config.stop_time
    noRotAtGoal: behavior_config.no_rotation_at_goal
    twoWayDrive: behavior_config.two_way_drive
    pubSkipNum: behavior_config.publish_skip_number
