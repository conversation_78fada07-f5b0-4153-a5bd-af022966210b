#pragma once

#include <string>
#include <memory>
#include <deque>
#include <mutex>
// #include "data_struct.h"

// #include "subscriber/imu_data_subscriber_base.h"
// #include "subscriber/gnss_data_subscriber_base.h"
// #include "subscriber/lidar_data_subscriber_base.h"
// #include "subscriber/odometry_subscriber_base.h"
#include "subscriber_base.h"
#include "publisher_base.h"

namespace communication {

// struct CommunicationConfig
// {
//     std::string imu_topic;        // IMU topic name
//     std::string gnss_topic;       // GNSS topic name
//     std::string lidar_topic;      // LiDAR topic name
//     std::string camera_topic;     // Camera topic name
// };
    

class CommunicationImpl; // Forward declaration of implementation class

class Communication {
public:
    Communication(const std::string& module_name);
    ~Communication();

public:
    bool Initialize(const std::string& config_file_path);

    //1. subscribers
    //1.1 subscribers for sensor data, such as IMU, GNSS, LiDAR
    std::shared_ptr<ImuDataSubscriberBase> CreateImuDataSubscriber(const std::string& imu_topic);
    std::shared_ptr<GnssDataSubscriberBase> CreateGnssDataSubscriber(const std::string& gnss_topic);
    std::shared_ptr<LidarDataSubscriberBase> CreateLidarDataSubscriber(const std::string& lidar_topic);
    std::shared_ptr<ImageDataSubscriberBase> CreateImageDataSubscriber(const std::string& camera_topic);// camera data subscriber

    //1.2 subscribers for algorithm data

    std::shared_ptr<OdometrySubscriberBase> CreateOdometrySubscriber(const std::string& topic);
    std::shared_ptr<IntDataSubscriberBase> CreateIntDataSubscriber(const std::string &topic);
    std::shared_ptr<DoubleDataSubscriberBase> CreateDoubleDataSubscriber(const std::string &topic);
    std::shared_ptr<StringDataSubscriberBase> CreateStringDataSubscriber(const std::string &topic);
    std::shared_ptr<BoolDataSubscriberBase> CreateBoolDataSubscriber(const std::string &topic);
    std::shared_ptr<CloudDataSubscriberBase> CreateCloudDataSubscriber(const std::string &topic);
    std::shared_ptr<PathDataSubscriberBase> CreatePathDataSubscriber(const std::string &topic);
    std::shared_ptr<PoseDataSubscriberBase> CreatePoseDataSubscriber(const std::string &topic);

    std::shared_ptr<CameraIntSubscriberBase> CreateCameraIntSubscriber(const std::string &topic); //Camera Intrinsics data subscriber
    std::shared_ptr<CameraExtSubscriberBase> CreateCameraExtSubscriber(const std::string &topic); //Camera Extrinsics data subscriber


    //2. publishers 
    //2.1 publishers for algorithm data
    std::shared_ptr<OdometryPublisherBase> CreateOdometryPublisher(const std::string& topic, 
                const std::string& frame_id = "map", const std::string& child_frame_id = "body", size_t max_buffer_size = 10);
    std::shared_ptr<IntDataPublisherBase> CreateIntDataPublisher(const std::string &topic, size_t max_buffer_size = 10);
    std::shared_ptr<DoubleDataPublisherBase> CreateDoubleDataPublisher(const std::string &topic, size_t max_buffer_size = 10);
    std::shared_ptr<StringDataPublisherBase> CreateStringDataPublisher(const std::string &topic, size_t max_buffer_size = 10);
    std::shared_ptr<BoolDataPublisherBase> CreateBoolDataPublisher(const std::string &topic, size_t max_buffer_size = 10);
    std::shared_ptr<CloudDataPublisherBase> CreateCloudDataPublisher(const std::string &topic, 
                const std::string& frame_id = "map", size_t max_buffer_size = 10);

    std::shared_ptr<PathDataPublisherBase> CreatePathDataPublisher(const std::string &topic,
                const std::string& frame_id = "map", size_t max_buffer_size = 10);

    std::shared_ptr<TwistDataPublisherBase> CreateTwistDataPublisher(const std::string &topic,
                const std::string& frame_id = "map", size_t max_buffer_size = 10);

    std::shared_ptr<PoseDataPublisherBase> CreatePoseDataPublisher(const std::string &topic, size_t max_buffer_size = 10);


    // Run the communication implementation
    void Run();
    
protected:
  
private:
  
    std::shared_ptr<CommunicationImpl> communication_impl_; // Pointer to the implementation class for communication
    std::string module_name_; // Name of the module for communication
    CommunicationType type_; // Type of communication (e.g., ROS1, ROS2, TCP)

    // CommunicationConfig config_; // Configuration for communication topics

};

} //namespace communication {
