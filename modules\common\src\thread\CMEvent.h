#ifndef _CM_EVENT_H_
#define _CM_EVENT_H_

#include "CM_pubhead.h"

#ifdef WIN32
#include "windows.h"
#else
#include <pthread.h>
#include <sys/time.h>
const int INFINITE = -1;
#endif


class CMEvent
{
public:    
    CMEvent(cx_bool bIsManualReset = true, cx_bool bInitialSignaled = false);
    ~CMEvent();    

public:
    cx_bool Create();    
    cx_bool Set();    
    cx_bool Reset();    
    cx_bool Wait(int cms); 
    cx_bool Destroy();
    cx_bool IsTriggered();

private:    
    cx_bool EnsureInitialized();    

private:
    cx_bool m_bIsManualReset;
    cx_bool m_bEventStatus;

#ifdef WIN32
    cx_handle m_hEvent;
#else
    cx_bool m_bMutexInitialized;
    pthread_mutex_t m_mutex;
    cx_bool m_bCondInitialized;
    pthread_cond_t m_cond;
#endif
};

#endif // _CM_EVENT_H_
