#include "path_follower.h"
#include <thread>
#include <chrono>
#include <iomanip> 

namespace path_follower {

// 构造函数
PathFollowerNoRos::PathFollowerNoRos(const std::string& config_path)
    : config_path_(config_path)
    , initialized_(false)
    , running_(false)
    , paused_(false)
{
    // 初始化变量
    initializeVariables();
}

// 析构函数
PathFollowerNoRos::~PathFollowerNoRos() {
    stopFollowing();
}

// 初始化函数
bool PathFollowerNoRos::initialize() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    
    if (initialized_) {
        return true;
    }
    
    try {
        // 加载配置
        if (!config_path_.empty()) {
            if (!loadConfig(config_path_)) {
                std::cerr << "Failed to load config from: " << config_path_ << std::endl;
                return false;
            }
        }
        
        initialized_ = true;
        std::cout << "PathFollowerNoRos initialization complete." << std::endl;
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "PathFollowerNoRos initialization failed: " << e.what() << std::endl;
        return false;
    }
}

// 加载配置
bool PathFollowerNoRos::loadConfig(const std::string& config_path) {
    if (config_path.empty()) {
        std::cout << "Using default configuration." << std::endl;
        return true;
    }
    
    try {
        if (config_path.find(".yaml") != std::string::npos || 
            config_path.find(".yml") != std::string::npos) {
            return loadYamlConfig(config_path);
        } else {
            std::cerr << "Unsupported config file format. Only YAML is supported." << std::endl;
            return false;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Failed to load config: " << e.what() << std::endl;
        return false;
    }
}

// 加载YAML配置
bool PathFollowerNoRos::loadYamlConfig(const std::string& config_path) {
    try {
        YAML::Node config = YAML::LoadFile(config_path);
        
        // 传感器配置
        if (config["sensor_config"]) {
            auto sensor_config = config["sensor_config"];
            if (sensor_config["offset_x"]) config_.sensorOffsetX = sensor_config["offset_x"].as<double>();
            if (sensor_config["offset_y"]) config_.sensorOffsetY = sensor_config["offset_y"].as<double>();
        }
        
        // 控制参数配置
        if (config["control_config"]) {
            auto control_config = config["control_config"];
            if (control_config["look_ahead_distance"]) config_.lookAheadDis = control_config["look_ahead_distance"].as<double>();
            if (control_config["yaw_rate_gain"]) config_.yawRateGain = control_config["yaw_rate_gain"].as<double>();
            if (control_config["stop_yaw_rate_gain"]) config_.stopYawRateGain = control_config["stop_yaw_rate_gain"].as<double>();
            if (control_config["max_yaw_rate"]) config_.maxYawRate = control_config["max_yaw_rate"].as<double>();
            if (control_config["max_speed"]) config_.maxSpeed = control_config["max_speed"].as<double>();
            if (control_config["max_acceleration"]) config_.maxAccel = control_config["max_acceleration"].as<double>();
        }
        
        // 切换参数配置
        if (config["switching_config"]) {
            auto switch_config = config["switching_config"];
            if (switch_config["switch_time_threshold"]) config_.switchTimeThre = switch_config["switch_time_threshold"].as<double>();
            if (switch_config["direction_diff_threshold"]) config_.dirDiffThre = switch_config["direction_diff_threshold"].as<double>();
            if (switch_config["stop_distance_threshold"]) config_.stopDisThre = switch_config["stop_distance_threshold"].as<double>();
            if (switch_config["slow_down_distance_threshold"]) config_.slowDwnDisThre = switch_config["slow_down_distance_threshold"].as<double>();
        }
        
        // 倾斜检测配置
        if (config["inclination_config"]) {
            auto incl_config = config["inclination_config"];
            if (incl_config["use_inclination_rate_to_slow"]) config_.useInclRateToSlow = incl_config["use_inclination_rate_to_slow"].as<bool>();
            if (incl_config["inclination_rate_threshold"]) config_.inclRateThre = incl_config["inclination_rate_threshold"].as<double>();
            if (incl_config["slow_rate_1"]) config_.slowRate1 = incl_config["slow_rate_1"].as<double>();
            if (incl_config["slow_rate_2"]) config_.slowRate2 = incl_config["slow_rate_2"].as<double>();
            if (incl_config["slow_time_1"]) config_.slowTime1 = incl_config["slow_time_1"].as<double>();
            if (incl_config["slow_time_2"]) config_.slowTime2 = incl_config["slow_time_2"].as<double>();
            if (incl_config["use_inclination_to_stop"]) config_.useInclToStop = incl_config["use_inclination_to_stop"].as<bool>();
            if (incl_config["inclination_threshold"]) config_.inclThre = incl_config["inclination_threshold"].as<double>();
            if (incl_config["stop_time"]) config_.stopTime = incl_config["stop_time"].as<double>();
        }
        
        // 行为参数配置
        if (config["behavior_config"]) {
            auto behavior_config = config["behavior_config"];
            if (behavior_config["no_rotation_at_goal"]) config_.noRotAtGoal = behavior_config["no_rotation_at_goal"].as<bool>();
            if (behavior_config["two_way_drive"]) config_.twoWayDrive = behavior_config["two_way_drive"].as<bool>();
            if (behavior_config["publish_skip_number"]) config_.pubSkipNum = behavior_config["publish_skip_number"].as<int>();
        }
        
        std::cout << "YAML configuration loaded successfully from: " << config_path << std::endl;
        return true;
    }
    catch (const YAML::Exception& e) {
        std::cerr << "YAML parsing error: " << e.what() << std::endl;
        return false;
    }
    catch (const std::exception& e) {
        std::cerr << "Error loading YAML config: " << e.what() << std::endl;
        return false;
    }
}

// 初始化变量
void PathFollowerNoRos::initializeVariables() {
    // 初始化所有原始变量（与原pathFollower.cpp一致）
    adjustmode = false;
    goalX = goalY = goalZ = 0.0;
    nav_start = 0;
    modifySpeed = 0.0;
    joyYaw = 0.0;
    vehicleX = vehicleY = vehicleZ = 0.0;
    vehicleRoll = vehiclePitch = vehicleYaw = 0.0;
    vehicleXRec = vehicleYRec = vehicleZRec = 0.0;
    vehicleRollRec = vehiclePitchRec = vehicleYawRec = 0.0;
    vehicleYawRate = vehicleSpeed = 0.0;
    odomTime = 0.0;
    slowInitTime = 0.0;
    stopInitTime = 0.0;
    pathPointID = 0;
    pathInit = false;
    navFwd = true;
    switchTime = 0.0;
    safetyStop = 0;
    rotinit = false;
    odometryTime = 0.0;
    pubSkipCount = 0;
}

// 获取当前时间
double PathFollowerNoRos::getCurrentTime() {
    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration<double>(duration).count();
}

// 数据输入接口实现
void PathFollowerNoRos::updateOdometry(const OdometryData& odom) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    
    odomTime = odom.header.stamp.toSec();
    
    // 计算姿态角
    double roll, pitch, yaw;
    odom.pose.orientation.toRPY(roll, pitch, yaw);  // 使用 toRPY 替代 toYaw
    
    vehicleRoll = roll;
    vehiclePitch = pitch;
    vehicleYaw = yaw;
    
    vehicleX = odom.pose.position.x - cos(yaw) * config_.sensorOffsetX + sin(yaw) * config_.sensorOffsetY;
    vehicleY = odom.pose.position.y - sin(yaw) * config_.sensorOffsetX - cos(yaw) * config_.sensorOffsetY;
    vehicleZ = odom.pose.position.z;
    
    if ((fabs(roll) > config_.inclThre * PI / 180.0 || fabs(pitch) > config_.inclThre * PI / 180.0) && config_.useInclToStop) {
        stopInitTime = odom.header.stamp.toSec();
    }
    
    if ((fabs(odom.twist.angular.x) > config_.inclRateThre * PI / 180.0 || 
         fabs(odom.twist.angular.y) > config_.inclRateThre * PI / 180.0) && config_.useInclRateToSlow) {
        slowInitTime = odom.header.stamp.toSec();
    }
    
    odometryTime = pcl::getTime();
}

void PathFollowerNoRos::updatePath(const PathData& path) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    
    int pathSize = path.poses.size();
    this->path.poses.resize(pathSize);
    
    for (int i = 0; i < pathSize; i++) {
        this->path.poses[i].pose.pose.position.x = path.poses[i].pose.pose.position.x;
        this->path.poses[i].pose.pose.position.y = path.poses[i].pose.pose.position.y;
        this->path.poses[i].pose.pose.position.z = path.poses[i].pose.pose.position.z;
    }
    
    // 记录接收到轨迹时刻的载体位姿信息
    vehicleXRec = vehicleX;
    vehicleYRec = vehicleY;
    vehicleZRec = vehicleZ;
    vehicleRollRec = vehicleRoll;
    vehiclePitchRec = vehiclePitch;
    vehicleYawRec = vehicleYaw;
    
    // 轨迹初始化完成
    pathPointID = 0;
    pathInit = true;
}

void PathFollowerNoRos::updateMode(const BoolMsg& mode) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    modeHandler(std::make_shared<BoolMsg>(mode));
}

void PathFollowerNoRos::updateGoal(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    goalHandler(std::make_shared<PoseStamped>(goal));
}

void PathFollowerNoRos::updateWebGoal(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    webgoalHandler(std::make_shared<PoseStamped>(goal));
}

void PathFollowerNoRos::updateStop(const Int8Msg& stop) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    stopHandler(std::make_shared<Int8Msg>(stop));
}

// 输出回调设置
void PathFollowerNoRos::setSpeedPublishCallback(TwistCallback callback) {
    speed_publish_callback_ = callback;
}

// 控制接口
void PathFollowerNoRos::startFollowing() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    running_ = true;
    paused_ = false;
}

void PathFollowerNoRos::stopFollowing() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    running_ = false;
    paused_ = false;
}

void PathFollowerNoRos::pauseFollowing() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    paused_ = true;
}

void PathFollowerNoRos::resumeFollowing() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    paused_ = false;
}

// 参数设置
void PathFollowerNoRos::setMaxSpeed(double speed) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.maxSpeed = speed;
}

void PathFollowerNoRos::setLookAheadDistance(double distance) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.lookAheadDis = distance;
}

void PathFollowerNoRos::setTwoWayDrive(bool enable) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.twoWayDrive = enable;
}

void PathFollowerNoRos::setNoRotationAtGoal(bool enable) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.noRotAtGoal = enable;
}

void PathFollowerNoRos::setYawRateGain(double gain) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.yawRateGain = gain;
}

void PathFollowerNoRos::setMaxYawRate(double rate) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.maxYawRate = rate;
}

void PathFollowerNoRos::setMaxAcceleration(double accel) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.maxAccel = accel;
}

// 状态查询
bool PathFollowerNoRos::isInitialized() const {
    return initialized_;
}

bool PathFollowerNoRos::isRunning() const {
    return running_;
}

bool PathFollowerNoRos::isPaused() const {
    return paused_;
}

bool PathFollowerNoRos::isPathValid() const {
    return pathInit && !path.empty();
}

bool PathFollowerNoRos::hasReachedGoal() const {
    double distance = sqrt(pow(vehicleX - goalX, 2) + pow(vehicleY - goalY, 2));
    return distance < config_.stopDisThre;
}

double PathFollowerNoRos::getDistanceToGoal() const {
    return sqrt(pow(vehicleX - goalX, 2) + pow(vehicleY - goalY, 2));
}

double PathFollowerNoRos::getCurrentSpeed() const {
    return vehicleSpeed;
}

double PathFollowerNoRos::getCurrentYawRate() const {
    return vehicleYawRate;
}

int PathFollowerNoRos::getCurrentPathPointID() const {
    return pathPointID;
}

PathFollowerConfig PathFollowerNoRos::getConfig() const {
    return config_;
}

// 原始回调函数实现 (保留原有算法逻辑)
void PathFollowerNoRos::odomHandler(const std::shared_ptr<OdometryData>& odomIn) {
    odomTime = odomIn->header.stamp.toSec();

    // 计算姿态角
    double roll, pitch, yaw;
    odomIn->pose.orientation.toRPY(roll, pitch, yaw);

    vehicleRoll = roll;
    vehiclePitch = pitch;
    vehicleYaw = yaw;
    vehicleX = odomIn->pose.position.x - cos(yaw) * config_.sensorOffsetX + sin(yaw) * config_.sensorOffsetY;
    vehicleY = odomIn->pose.position.y - sin(yaw) * config_.sensorOffsetX - cos(yaw) * config_.sensorOffsetY;
    vehicleZ = odomIn->pose.position.z;

    if ((fabs(roll) > config_.inclThre * PI / 180.0 || fabs(pitch) > config_.inclThre * PI / 180.0) && config_.useInclToStop) {
        stopInitTime = odomIn->header.stamp.toSec();
    }

    if ((fabs(odomIn->twist.angular.x) > config_.inclRateThre * PI / 180.0 ||
         fabs(odomIn->twist.angular.y) > config_.inclRateThre * PI / 180.0) && config_.useInclRateToSlow) {
        slowInitTime = odomIn->header.stamp.toSec();
    }

    odometryTime = pcl::getTime();
}

void PathFollowerNoRos::pathHandler(const std::shared_ptr<PathData>& pathIn) {
    int pathSize = pathIn->poses.size();
    path.poses.resize(pathSize);
    for (int i = 0; i < pathSize; i++) {
        path.poses[i].pose.pose.position.x = pathIn->poses[i].pose.pose.position.x;
        path.poses[i].pose.pose.position.y = pathIn->poses[i].pose.pose.position.y;
        path.poses[i].pose.pose.position.z = pathIn->poses[i].pose.pose.position.z;
    }

    // 记录接收到轨迹时刻的载体位姿信息
    vehicleXRec = vehicleX;
    vehicleYRec = vehicleY;
    vehicleZRec = vehicleZ;
    vehicleRollRec = vehicleRoll;
    vehiclePitchRec = vehiclePitch;
    vehicleYawRec = vehicleYaw;

    // 轨迹初始化完成
    pathPointID = 0;
    pathInit = true;
}

void PathFollowerNoRos::modeHandler(const std::shared_ptr<BoolMsg>& mode) {
    adjustmode = mode->data;
}

void PathFollowerNoRos::goalHandler(const std::shared_ptr<PoseStamped>& goal) {
    goalX = goal->pose.position.x;
    goalY = goal->pose.position.y;
    // goalZ = goal->pose.position.z;
    nav_start = 1;

    // 前进后退切换
    config_.twoWayDrive = false;
    modifySpeed = config_.maxSpeed;
    navFwd = true;
    switchTime = 0;
    rotinit = false;
    config_.dirDiffThre = 0.1;
}

void PathFollowerNoRos::webgoalHandler(const std::shared_ptr<PoseStamped>& goal) {
    goalX = goal->pose.position.x;
    goalY = goal->pose.position.y;
    // goalZ = goal->pose.position.z;
    nav_start = 1;

    // 前进后退切换
    config_.twoWayDrive = false;
    modifySpeed = config_.maxSpeed;
    navFwd = true;
    switchTime = 0;
    rotinit = false;
    config_.dirDiffThre = 0.1;
}

void PathFollowerNoRos::stopHandler(const std::shared_ptr<Int8Msg>& stop) {
    safetyStop = stop->data;
}

// 主循环方法
void PathFollowerNoRos::controlLoop() {
    const double loop_rate = 100.0; // 100Hz
    const auto sleep_duration = std::chrono::duration<double>(1.0 / loop_rate);

    while (running_ && !paused_) {
        try {
            processOnce();
            std::this_thread::sleep_for(sleep_duration);
        }
        catch (const std::exception& e) {
            std::cerr << "Control loop error: " << e.what() << std::endl;
            break;
        }
    }
}

// 处理一次路径跟随 (保留原有主要算法逻辑)
void PathFollowerNoRos::processOnce() {
    std::lock_guard<std::mutex> lock(data_mutex_);

    if (!initialized_ || !running_ || paused_) {
        return;
    }

    // 执行主要的路径跟随算法
    performPathFollowing();
}

// 主要的路径跟随算法 (保留原pathFollower.cpp的核心逻辑)
void PathFollowerNoRos::performPathFollowing() {
    TwistData cmd_vel;

    // 安全停止处理
    if (safetyStop == 1) {
        cmd_vel.setZero();
        if (speed_publish_callback_) {
            speed_publish_callback_(std::make_shared<TwistData>(cmd_vel));
        }
        return;
    }

    // 路径跟随主逻辑
    if (pathInit && !adjustmode && nav_start == 1 && safetyStop != 1) {
        // 计算接收到轨迹时的位置在当前载体系下的坐标
        float vehicleXRel = cos(vehicleYawRec) * (vehicleX - vehicleXRec) + sin(vehicleYawRec) * (vehicleY - vehicleYRec);
        float vehicleYRel = -sin(vehicleYawRec) * (vehicleX - vehicleXRec) + cos(vehicleYawRec) * (vehicleY - vehicleYRec);

        // 计算接收到轨迹时的位置距轨迹最后一个点的距离
        int pathSize = path.poses.size();
        float endDisX = path.poses[pathSize - 1].pose.pose.position.x - vehicleXRel;
        float endDisY = path.poses[pathSize - 1].pose.pose.position.y - vehicleYRel;
        float endDis = sqrt(endDisX * endDisX + endDisY * endDisY);

        // 寻找前视距离范围内的最后一个点
        float disX, disY, dis;
        while (pathPointID < pathSize - 1) {
            disX = path.poses[pathPointID].pose.pose.position.x - vehicleXRel;
            disY = path.poses[pathPointID].pose.pose.position.y - vehicleYRel;
            dis = sqrt(disX * disX + disY * disY);
            if (dis < config_.lookAheadDis) pathPointID++;
            else break;
        }

        // 计算接收到轨迹时的位置距前视距离内最后一个点的距离以及角度
        disX = path.poses[pathPointID].pose.pose.position.x - vehicleXRel;
        disY = path.poses[pathPointID].pose.pose.position.y - vehicleYRel;
        dis = sqrt(disX * disX + disY * disY);
        float pathDir = atan2(disY, disX);

        // 计算当前位置与前视距离最后一个点的夹角
        float dirDiff = vehicleYaw - vehicleYawRec - pathDir;
        if (!rotinit) dirDiff = vehicleYaw - atan2(goalY - vehicleY, goalX - vehicleX);

        // 保证夹角在-pi到pi内
        if (dirDiff > PI) dirDiff -= 2 * PI;
        else if (dirDiff < -PI) dirDiff += 2 * PI;
        if (dirDiff > PI) dirDiff -= 2 * PI;
        else if (dirDiff < -PI) dirDiff += 2 * PI;

        // 判断是否进行双向驱动
        if (config_.twoWayDrive) {
            double time = getCurrentTime();
            // 夹角大于90度且当前处于前向运动转换为后向运动
            if (fabs(dirDiff) > PI / 2 && navFwd && time - switchTime > config_.switchTimeThre) {
                navFwd = false;
                switchTime = time;
            }
            // 夹角小于90度且当前处于后向运动转换为前向运动
            else if (fabs(dirDiff) < PI / 2 && !navFwd && time - switchTime > config_.switchTimeThre) {
                navFwd = true;
                switchTime = time;
            }
        }

        // 计算载体距离终点距离
        double distance = sqrt(pow(vehicleX - goalX, 2) + pow(vehicleY - goalY, 2));

        // 计算载体在三米内减速到最小速度所需的加速度
        double a = (config_.maxSpeed * config_.maxSpeed - 0.1) / (2 * 3);

        // 判断是否进入减速范围
        if (distance < 3) modifySpeed = sqrt(2 * a * distance + 0.1);
        else modifySpeed = config_.maxSpeed;

        float joySpeed2 = modifySpeed;

        // 判断前后向运动，若后向将角度设置在90度内，速度设置为负
        if (!navFwd) {
            dirDiff += PI;
            if (dirDiff > PI) dirDiff -= 2 * PI;
            joySpeed2 *= -1;
        }

        // 判断线速度是否为0，若为0则采用停止转角增益，若不为0则采用普通转角增益
        if (fabs(vehicleSpeed) < 2.0 * config_.maxAccel / 100.0)
            vehicleYawRate = -config_.stopYawRateGain * dirDiff;
        else
            vehicleYawRate = -config_.yawRateGain * dirDiff;

        // 对载体角速度进行限幅
        if (vehicleYawRate > config_.maxYawRate * PI / 180.0)
            vehicleYawRate = config_.maxYawRate * PI / 180.0;
        else if (vehicleYawRate < -config_.maxYawRate * PI / 180.0)
            vehicleYawRate = -config_.maxYawRate * PI / 180.0;

        // 若局部距离小于阈值角速度为0
        if (pathSize <= 1 || (dis < config_.stopDisThre && config_.noRotAtGoal))
            vehicleYawRate = 0;

        // 若局部距离小于减速距离则进行减速
        if (pathSize <= 1)
            joySpeed2 = 0;
        else if (endDis < config_.slowDwnDisThre)
            joySpeed2 *= endDis / config_.slowDwnDisThre;

        // 倾斜减速处理
        float joySpeed3 = joySpeed2;
        if (odomTime < slowInitTime + config_.slowTime1 && slowInitTime > 0)
            joySpeed3 *= config_.slowRate1;
        else if (odomTime < slowInitTime + config_.slowTime1 + config_.slowTime2 && slowInitTime > 0)
            joySpeed3 *= config_.slowRate2;

        // 若夹角小于阈值且再停止范围外则正常加减速
        if (fabs(dirDiff) < config_.dirDiffThre && dis > config_.stopDisThre) {
            if (vehicleSpeed < joySpeed3) vehicleSpeed += config_.maxAccel / 100.0;
            else if (vehicleSpeed > joySpeed3) vehicleSpeed -= config_.maxAccel / 100.0;
            rotinit = true;
            config_.dirDiffThre = 0.1;
        }
        // 若夹角大于阈值，则先考虑转弯再前进
        else {
            if (vehicleSpeed > 0) vehicleSpeed -= config_.maxAccel / 100.0;
            else if (vehicleSpeed < 0) vehicleSpeed += config_.maxAccel / 100.0;
        }

        // 倾斜停止处理
        if (odomTime < stopInitTime + config_.stopTime && stopInitTime > 0) {
            vehicleSpeed = 0;
            vehicleYawRate = 0;
        }

        // 改变速度输出频率
        pubSkipCount--;
        if (pubSkipCount < 0) {
            // 发布用于驱动的规划速度信息
            if (fabs(vehicleSpeed) <= config_.maxAccel / 100.0)
                cmd_vel.linear.x = 0;
            else
                cmd_vel.linear.x = vehicleSpeed;

            if (fabs(vehicleYawRate) <= 0.01)
                cmd_vel.angular.z = 0;
            else
                cmd_vel.angular.z = vehicleYawRate;

            // 停止信息速度清零
            if (safetyStop == 1 || pcl::getTime() - odometryTime > 0.5) {
                cmd_vel.linear.x = 0;
                cmd_vel.angular.z = 0;
            }

            // 发布速度命令
            if (speed_publish_callback_) {
                speed_publish_callback_(std::make_shared<TwistData>(cmd_vel));
            }

            pubSkipCount = config_.pubSkipNum;
        }
    }
}

// 打印状态
void PathFollowerNoRos::printStatus() {
    std::cout << "PathFollowerNoRos Status:" << std::endl;
    std::cout << "  Initialized: " << (initialized_ ? "Yes" : "No") << std::endl;
    std::cout << "  Running: " << (running_ ? "Yes" : "No") << std::endl;
    std::cout << "  Paused: " << (paused_ ? "Yes" : "No") << std::endl;
    std::cout << "  Vehicle Position: (" << vehicleX << ", " << vehicleY << ", " << vehicleZ << ")" << std::endl;
    std::cout << "  Vehicle Orientation: " << vehicleYaw * 180.0 / PI << " deg" << std::endl;
    std::cout << "  Goal Position: (" << goalX << ", " << goalY << ", " << goalZ << ")" << std::endl;
    std::cout << "  Current Speed: " << vehicleSpeed << " m/s" << std::endl;
    std::cout << "  Current Yaw Rate: " << vehicleYawRate * 180.0 / PI << " deg/s" << std::endl;
    std::cout << "  Path Valid: " << (isPathValid() ? "Yes" : "No") << std::endl;
    std::cout << "  Navigation Started: " << (nav_start ? "Yes" : "No") << std::endl;
    std::cout << "  Adjust Mode: " << (adjustmode ? "Yes" : "No") << std::endl;
    std::cout << "  Safety Stop: " << safetyStop << std::endl;
    std::cout << "  Distance to Goal: " << getDistanceToGoal() << " m" << std::endl;
}

} // namespace path_follower
