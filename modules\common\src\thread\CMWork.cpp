#include "CMWork.h"

CMWork::CMWork()
{
    m_pfnThreadProc = NULL;
    m_pContext = NULL;
}

#ifdef WIN32
CMWork::CMWork(ThreadProc* pfnThreadProc, LPVOID const pContext)
{
    m_pfnThreadProc = pfnThreadProc;
    m_pContext = pContext;
}
#endif // Win32


CMWork::~CMWork()
{

}

cx_int CMWork::SetThreadProc(ThreadProc* pfnThreadProc)
{
    m_pfnThreadProc = pfnThreadProc;

    return 0;
}

cx_int CMWork::SetThreadContext(LPVOID pContext)
{
    m_pContext = pContext;

    return 0;
}

ThreadProc* CMWork::GetThreadProc()
{
    return m_pfnThreadProc;
}

LPVOID  CMWork::GetContext()
{
    return m_pContext;
}

cx_int CMWork::SetName(cx_string strName)
{
    m_strName = strName;

    return 0;
}

const cx_string& CMWork::GetName() const
{
    return m_strName;
}

cx_int CMWork::Run()
{
    ASSERT(m_pfnThreadProc);
    if (NULL == m_pfnThreadProc)
    {
        return -1;
    }

#ifdef WIN32
    (*m_pfnThreadProc)(NULL, m_pContext);
#else
    (*m_pfnThreadProc)(m_pContext);
#endif

    //delete this; // ����ִ�����ʱɾ��

    return 0;
}
