#ifndef _COMMON_H_
#define _COMMON_H_
#include "CM_pubhead.h"

#include "CMEvent.h"
#include "CMMutex.h"
#include "CMAutoMutex.h"
#include "CMRWLock.h"

#include "CMThread.h"
#include "CMWork.h"
#include "CMThreadPool.h"

#include "CMSingleton.h"
#include "CMObserver.h"

#include "CMString.h"
#include "CMBits.h"
#include "CMTemplate.h"

#include "CMPath.h"

#include "CMTime.h"
#include "CMMessage.h"
#include "config.h"

cx_int CM_Initialize(string strDataPath = "");

Config& CM_GetConfig();

cx_int CM_Destroy();

#endif // !_COMMON_H_

