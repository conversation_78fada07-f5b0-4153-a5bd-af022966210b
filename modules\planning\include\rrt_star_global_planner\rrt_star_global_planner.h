#ifndef RRT_STAR_GLOBAL_PLANNER_H
#define RRT_STAR_GLOBAL_PLANNER_H


#include <cmath>
#include <vector>
#include <string>
#include <memory>
#include <functional>
#include <iostream>
#include <fstream>
#include <random>
#include <thread>
#include <mutex>
#include <chrono>

// YAML解析库 (可选)
#ifdef USE_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// 去ROS化的数据结构定义

/**
 * @brief 去ROS化的位姿数据结构 (替代geometry_msgs::PoseStamped)
 */
struct PoseStamped {
    struct Header {
        double stamp;           // 时间戳
        std::string frame_id;   // 坐标系ID
        Header() : stamp(0.0), frame_id("map") {}
    } header;
    
    struct Pose {
        struct Position {
            double x, y, z;
            Position() : x(0), y(0), z(0) {}
            Position(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
        } position;
        
        struct Orientation {
            double x, y, z, w;  // 四元数
            Orientation() : x(0), y(0), z(0), w(1) {}
            Orientation(double x_, double y_, double z_, double w_) : x(x_), y(y_), z(z_), w(w_) {}
        } orientation;
    } pose;
    
    PoseStamped() {}
    PoseStamped(double x, double y, double yaw, const std::string& frame = "map") {
        header.frame_id = frame;
        header.stamp = getCurrentTime();
        pose.position.x = x;
        pose.position.y = y;
        pose.position.z = 0.0;
        
        // 将yaw角转换为四元数
        pose.orientation.x = 0.0;
        pose.orientation.y = 0.0;
        pose.orientation.z = sin(yaw / 2.0);
        pose.orientation.w = cos(yaw / 2.0);
    }
    
    static double getCurrentTime() {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration<double>(duration).count();
    }
    
    // 获取yaw角
    double getYaw() const {
        return 2.0 * atan2(pose.orientation.z, pose.orientation.w);
    }
};

/**
 * @brief 去ROS化的路径数据结构 (替代nav_msgs::Path)
 */
struct Path {
    struct Header {
        double stamp;
        std::string frame_id;
        Header() : stamp(0.0), frame_id("map") {}
    } header;
    
    std::vector<PoseStamped> poses;
    
    Path() {}
    
    void clear() { poses.clear(); }
    size_t size() const { return poses.size(); }
    bool empty() const { return poses.empty(); }
    void resize(size_t size) { poses.resize(size); }
    
    void addPose(double x, double y, double yaw = 0.0) {
        poses.emplace_back(x, y, yaw, header.frame_id);
    }
};

/**
 * @brief 去ROS化的可视化标记结构 (替代visualization_msgs::Marker)
 */
struct Marker {
    struct Header {
        double stamp;
        std::string frame_id;
        Header() : stamp(0.0), frame_id("map") {}
    } header;
    
    enum Type {
        POINTS = 8,
        LINE_STRIP = 4,
        LINE_LIST = 5
    };
    
    enum Action {
        ADD = 0,
        DELETE = 2,
        DELETEALL = 3
    };
    
    int id;
    Type type;
    Action action;
    
    struct Scale {
        double x, y, z;
        Scale() : x(0.1), y(0.1), z(0.1) {}
        Scale(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
    } scale;
    
    struct Color {
        double r, g, b, a;
        Color() : r(1.0), g(0.0), b(0.0), a(1.0) {}
        Color(double r_, double g_, double b_, double a_) : r(r_), g(g_), b(b_), a(a_) {}
    } color;
    
    struct Point {
        double x, y, z;
        Point() : x(0), y(0), z(0) {}
        Point(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
    };
    
    std::vector<Point> points;
    
    Marker() : id(0), type(POINTS), action(ADD) {}
    
    void clear() { points.clear(); }
    void addPoint(double x, double y, double z = 0.0) {
        points.emplace_back(x, y, z);
    }
};

/**
 * @brief 去ROS化的代价地图结构 (替代costmap_2d::Costmap2D)
 */
struct Costmap2D {
    std::vector<std::vector<unsigned char>> data;
    double resolution;      // 分辨率 (m/pixel)
    double origin_x, origin_y;  // 原点坐标
    int width, height;      // 地图尺寸
    
    Costmap2D() : resolution(0.05), origin_x(0), origin_y(0), width(0), height(0) {}
    Costmap2D(int w, int h, double res, double ox = 0.0, double oy = 0.0)
        : resolution(res), origin_x(ox), origin_y(oy), width(w), height(h) {
        data.resize(height, std::vector<unsigned char>(width, 0));
    }
    
    bool worldToMap(double wx, double wy, int& mx, int& my) const {
        mx = static_cast<int>((wx - origin_x) / resolution);
        my = static_cast<int>((wy - origin_y) / resolution);
        return (mx >= 0 && mx < width && my >= 0 && my < height);
    }
    
    bool mapToWorld(int mx, int my, double& wx, double& wy) const {
        if (mx < 0 || mx >= width || my < 0 || my >= height) return false;
        wx = origin_x + (mx + 0.5) * resolution;
        wy = origin_y + (my + 0.5) * resolution;
        return true;
    }
    
    unsigned char getCost(int mx, int my) const {
        if (mx < 0 || mx >= width || my < 0 || my >= height) return 255;
        return data[my][mx];
    }
    
    void setCost(int mx, int my, unsigned char cost) {
        if (mx >= 0 && mx < width && my >= 0 && my < height) {
            data[my][mx] = cost;
        }
    }
    
    bool loadFromFile(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) return false;
        
        file >> width >> height >> resolution >> origin_x >> origin_y;
        data.resize(height, std::vector<unsigned char>(width));
        
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                int cost;
                file >> cost;
                data[y][x] = static_cast<unsigned char>(cost);
            }
        }
        
        file.close();
        return true;
    }
    
    void saveToFile(const std::string& filename) const {
        std::ofstream file(filename);
        if (!file.is_open()) return;
        
        file << width << " " << height << " " << resolution << " " << origin_x << " " << origin_y << std::endl;
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                file << static_cast<int>(data[y][x]) << " ";
            }
            file << std::endl;
        }
        
        file.close();
    }
};

/**
 * @brief RRT*节点结构 (保持原有结构)
 */
struct Node {
    double x;
    double y;
    int node_id;
    int parent_id;
    double cost;
    
    Node() : x(0), y(0), node_id(-1), parent_id(-1), cost(0.0) {}
    Node(double x_, double y_, int id = -1, int parent = -1, double c = 0.0)
        : x(x_), y(y_), node_id(id), parent_id(parent), cost(c) {}
    
    bool operator==(const Node& node) const {
        return (fabs(x - node.x) < 0.0001) && (fabs(y - node.y) < 0.0001) && 
               (node_id == node.node_id) && (parent_id == node.parent_id) && 
               (fabs(cost - node.cost) < 0.0001);
    }

    bool operator!=(const Node& node) const {
        if ((fabs(x - node.x) > 0.0001) || (fabs(y - node.y) > 0.0001) || 
            (node_id != node.node_id) || (parent_id != node.parent_id) || 
            (fabs(cost - node.cost) > 0.0001))
            return true;
        else
            return false;
    }
};

/**
 * @brief 路径获取模式枚举 (保持原有枚举)
 */
enum GetPlanMode {
    TREE1 = 1,
    TREE2 = 2,
    CONNECT1TO2 = 3,
    CONNECT2TO1 = 4,
};

/**
 * @brief 去ROS化的发布器接口
 */
template<typename T>
class Publisher {
public:
    using CallbackType = std::function<void(const T&)>;
    
    Publisher() = default;
    
    void setCallback(CallbackType callback) {
        callback_ = callback;
    }
    
    void publish(const T& message) {
        if (callback_) {
            callback_(message);
        }
    }
    
private:
    CallbackType callback_;
};

namespace RRTstar_planner {

/**
 * @brief 去ROS化的RRT*规划器类 (保留所有原有功能和函数、变量)
 */
class RRTstarPlannerNoRos {
public:
    /**
     * @brief Default constructor of the plugin
     */
    RRTstarPlannerNoRos();

    RRTstarPlannerNoRos(std::string name, Costmap2D* costmap);

    /**
     * @brief  Initialization function for the PlannerCore object
     * @param  name The name of this planner
     * @param  costmap A pointer to the costmap to use for planning
     */
    void initialize(std::string name, Costmap2D* costmap);

    /**
     * @brief Given a goal pose in the world, compute a plan
     * @param start The start pose
     * @param goal The goal pose
     * @param plan The plan... filled by the planner
     * @return True if a valid plan was found, false otherwise
     */
    bool makePlan(const PoseStamped& start,
                  const PoseStamped& goal,
                  std::vector<PoseStamped>& plan);

    void getPathFromTree1ConnectTree2(std::vector<Node>& tree1,
                                      std::vector<Node>& tree2,
                                      Node& connect_node,
                                      std::vector<PoseStamped>& plan);

    void getPathFromTree(std::vector<Node>& tree1,
                         std::vector<Node>& tree2,
                         Node& connect_node,
                         std::vector<PoseStamped>& plan,
                         GetPlanMode mode);
    
    /*
     * @brief Compute the euclidean distance (straight-line distance) between two points
     * @param px1 point 1 x
     * @param py1 point 1 y
     * @param px2 point 2 x
     * @param py2 point 2 y
     * @return the distance computed
     */
    double distance(double px1, double py1, double px2, double py2);

    /**
     * @brief Generate random points.
     * @return the a random point in the map.
     */
    std::pair<double, double> sampleFree(); //随机采样点

    /**
     * @brief Check if there is a collision.
     * @param x coordinate (cartesian system)
     * @param y coordinate (cartesian system)
     * @return True is the point collides and false otherwise
     */
    bool collision(double x, double y); //是否为障碍物

    /**
     * @brief Check whether there are obstacles around.
     * @param x coordinate
     * @param y coordinate
     * @return True is the there are obstacles around
     */
    bool isAroundFree(double wx, double wy);

    bool isConnect(Node new_node, std::vector<Node>& another_tree, std::vector<Node>& current_tree, Node& connect_node);

    /**
     * @brief Given the nodes set and an point the function returns the closest node of the node
     * @param nodes the set of nodes
     * @param p_rand the random point (x,y) in the plane
     * return the closest node
     */
    Node getNearest(std::vector<Node> nodes, std::pair<double, double> p_rand); //搜索最近的节点

    /**
     * @brief Select the best parent. Check if there is any node around the newnode with cost less than its parent node cost. 
     * If yes choose this less cost node as the new parent of the newnode.
     * @param nn the parent of the newnode
     * @param newnode the node that will checked if there is a better parent for it
     * @param nodes the set of nodes
     * @return the same newnode with the best parent node
     * 
     */
    Node chooseParent(Node nn, Node newnode, std::vector<Node> nodes); //选择父节点

    /*
     * 该功能检查周围所有节点的父节点的开销是否仍小于新节点。
     * 如果存在父节点成本较高的节点，则该节点的新父节点现在是newnode。
     * 参数:nodes节点集。
     * 参数:newnode 新节点
     */
    void rewire(std::vector<Node>& nodes, Node newnode);

    /*
     * @brief The function generate the new point between the epsilon_min and epsilon_max along the line p_rand and nearest node. 
     *        This new point is a node candidate. It will a node if there is no obstacles between its nearest node and itself.
     * @param px1 point 1 x
     * @param py1 point 1 y
     * @param px2 point 2 x
     * @param py2 point 2 y
     * @return the new point
     */
    std::pair<double, double> steer(double x1, double y1, double x2, double y2); //生成新的树枝

    bool obstacleFree(Node node_nearest, double px, double py); //检查树枝是否碰撞障碍物

    /**
     * @brief Check if the distance between the goal and the newnode is less than the goal_radius. If yes the newnode is the goal.
     * @param px1 point 1 x
     * @param py1 point 1 y
     * @param px2 point 2 x
     * @param py2 point 2 y
     * *@return True if distance is less than the xy tolerance (GOAL_RADIUS), False otherwise
     */
    bool pointCircleCollision(double x1, double y1, double x2, double y2, double radius);

    void optimizationOrientation(std::vector<PoseStamped>& plan);

    void insertPointForPath(std::vector<std::pair<double, double>>& pathin, double param);

    int optimizationPath(std::vector<std::pair<double, double>>& plan, double movement_angle_range = M_PI/4); //优化路径

    bool isLineFree(const std::pair<double, double> p1, const std::pair<double, double> p2);

    void cutPathPoint(std::vector<std::pair<double, double>>& plan); //优化路径

    double inline normalizeAngle(double val, double min = -M_PI, double max = M_PI); //标准化角度

    void pubTreeMarker(Publisher<Marker>& marker_pub, Marker marker, int id);
    
    // 新增的去ROS化接口
    void setMapFromFile(const std::string& map_file);
    void setMapFromData(const std::vector<std::vector<unsigned char>>& map_data, 
                       double resolution, double origin_x, double origin_y);
    
    // 回调函数设置
    void setPathPublishCallback(std::function<void(const std::vector<PoseStamped>&)> callback);
    void setMarkerPublishCallback(std::function<void(const Marker&)> callback);
    void setAccessablePublishCallback(std::function<void(bool)> callback);
    
    // 状态查询
    bool isInitialized() const { return initialized_; }
    std::string getFrameId() const { return frame_id_; }
    double getResolution() const { return resolution_; }
    
    // 参数设置
    void setMaxNodesNum(size_t max_nodes) { max_nodes_num_ = max_nodes; }
    void setPlanTimeout(double timeout) { plan_time_out_ = timeout; }
    void setSearchRadius(double radius) { search_radius_ = radius; }
    void setGoalRadius(double radius) { goal_radius_ = radius; }
    void setEpsilonMin(double epsilon) { epsilon_min_ = epsilon; }
    void setEpsilonMax(double epsilon) { epsilon_max_ = epsilon; }
    void setPathPointSpacing(double spacing) { path_point_spacing_ = spacing; }
    void setAngleDifference(double angle) { angle_difference_ = angle; }

protected:
    Costmap2D* costmap_;
    std::string frame_id_;
    Publisher<std::vector<PoseStamped>> plan_pub_;
    Publisher<bool> accessable_pub_;

private:
    Marker marker_tree_;
    Marker marker_tree_2_;
    Publisher<Marker> marker_pub_;
    
    // 保留所有原有变量
    size_t max_nodes_num_;
    double plan_time_out_;
    double search_radius_;
    double goal_radius_;
    double epsilon_min_;
    double epsilon_max_;

    //路径优化参数
    double path_point_spacing_;
    double angle_difference_;

    double resolution_;
    bool initialized_;
    
    // 随机数生成器
    std::mt19937 rng_;
    std::uniform_real_distribution<double> uniform_dist_;
    
    // 回调函数
    std::function<void(const std::vector<PoseStamped>&)> path_callback_;
    std::function<void(const Marker&)> marker_callback_;
    std::function<void(bool)> accessable_callback_;
};

} // RRTstar_planner namespace

#endif // RRT_STAR_GLOBAL_PLANNER_H
