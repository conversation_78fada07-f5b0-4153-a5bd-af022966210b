#include "CM_pubhead.h"
#include "Common.h"

cx_int CM_Initialize(string strDataPath)
{
    fprintf(stderr, ">>>> LOC debug >>>> CM_Initialize   strDataPath: %s  \n", strDataPath.c_str());

    cx_char szBuf[_MAX_PATH] = { 0 };

#ifdef WIN32
    GetModuleFileName(NULL, szBuf, _MAX_PATH);
#else
    readlink("/proc/self/exe", szBuf, _MAX_PATH);
#endif // WIN32

    SetModuleFileName(szBuf);

    std::string strExePath = szBuf;
    if(strDataPath.empty())
    {
        strExePath = strExePath.substr(0, strExePath.rfind(PATH_SEPARATOR));
        fprintf(stderr, ">>>> LOC debug >>>> strDataPath is empty, SetExePath: %s  \n", strExePath.c_str());
        SetExePath(strExePath.c_str());
    }

#ifdef USING_LOG4CPLUS
    InitializeLogger();
#endif

    // Load config parameters.
    cx_string strConfigFile;
    GetExePath(strConfigFile);
    strConfigFile += "/config/lslidar.yaml";
    fprintf(stderr, ">>>> LOC debug >>>> SetConfigPath: %s  \n", strConfigFile.c_str());
    Config::getInstance().init(strConfigFile);

    return 0;
}

Config& CM_GetConfig()
{
    return Config::getInstance();
}

cx_int CM_Destroy()
{
    CMThreadPool* pThreadPool = GetSingleton4ThreadPool();
    DELETE_S(pThreadPool);


    return 0;
}
