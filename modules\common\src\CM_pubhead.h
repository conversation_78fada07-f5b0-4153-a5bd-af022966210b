﻿#if !defined(_CM_PUBHEAD_H_)
#define _CM_PUBHEAD_H_

#include <algorithm>
#include <limits>
#include <string>
#include <vector>
#include <list>
#include <deque>
#include <map>
#include <set>

using namespace std;

#include "CM_platform.h"
#include "CM_types.h"
#include "CM_debug.h"
#include "CM_const.h"


#ifndef PI
	#define PI		(3.14159265358979323846f)
#endif

const cx_double LONGITUDE_MIN   = 73.666667f;
const cx_double LONGITUDE_MAX   = 135.041667f;
const cx_double LATITUDE_MIN    = 3.866667f;
const cx_double LATITUDE_MAX    = 53.550000f;

#endif // !defined(_CM_PUBHEAD_H_)
