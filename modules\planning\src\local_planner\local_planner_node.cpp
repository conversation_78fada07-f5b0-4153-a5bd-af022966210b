#include "local_planner.h"
#include <iostream>
#include <signal.h>
#include <thread>

using namespace local_planner;

// 全局变量用于信号处理
LocalPlannerNoRos* g_planner = nullptr;
bool g_running = true;

// 信号处理函数
void signalHandler(int signum) {
    std::cout << "\n收到信号 " << signum << "，正在关闭局部规划器..." << std::endl;
    g_running = false;
    if (g_planner) {
        g_planner->stopPlanning();
    }
}

int main(int argc, char** argv) {
    std::cout << "=== 局部规划器节点启动 ===" << std::endl;
    
    // 注册信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    try {
        // 解析命令行参数
        //std::string config_path = "";
        std::string config_path = "../config/local_planner.yaml";
        if (argc > 1) {
            config_path = argv[1];
            std::cout << "使用配置文件: " << config_path << std::endl;
        } else {
            std::cout << "使用默认配置" << std::endl;
        }
        
        // 创建局部规划器
        std::cout << "\n🚀 创建局部规划器..." << std::endl;
        LocalPlannerNoRos planner(config_path);
        g_planner = &planner;
        
        // 设置输出回调函数
        std::cout << "\n📡 设置回调函数..." << std::endl;
        
        // 模式发布回调
        planner.setModePublishCallback([](const std::shared_ptr<const BoolMsg>& mode) {
            std::cout << "发布调整模式: " << (mode->data ? "精调" : "粗调") << std::endl;
        });
        
        // 路径发布回调
        planner.setPathPublishCallback([](const std::shared_ptr<const PathData>& path) {
            std::cout << "发布局部路径，点数: " << path->poses.size() << std::endl;
        });
        
        // 停止信号发布回调
        planner.setStopPublishCallback([](const std::shared_ptr<const Int8Msg>& stop) {
            std::cout << "发布停止信号: " << static_cast<int>(stop->data) << std::endl;
        });
        
        // 内部停止信号发布回调
        planner.setInnerStopPublishCallback([](const std::shared_ptr<const Int8Msg>& stop) {
            std::cout << "发布内部停止信号: " << static_cast<int>(stop->data) << std::endl;
        });
        
        // 重规划信号发布回调
        planner.setReplanPublishCallback([](const std::shared_ptr<const Int8Msg>& replan) {
            std::cout << "发布重规划信号: " << static_cast<int>(replan->data) << std::endl;
        });
        
        // 节点就绪信号发布回调
        planner.setNodeReadyPublishCallback([](const std::shared_ptr<const BoolMsg>& ready) {
            std::cout << "发布节点就绪信号: " << (ready->data ? "就绪" : "未就绪") << std::endl;
        });
        
        // 自由路径发布回调
        planner.setFreePathsPublishCallback([](const std::shared_ptr<const PointCloud2Data>& cloud) {
            std::cout << "发布自由路径点云，数据大小: " << cloud->data.size() << " bytes" << std::endl;
        });
        
        // 初始化规划器
        std::cout << "\n🔧 初始化局部规划器..." << std::endl;
        if (!planner.initialize()) {
            std::cerr << "❌ 局部规划器初始化失败！" << std::endl;
            return -1;
        }
        
        // 启动规划器
        std::cout << "\n▶️  启动局部规划器..." << std::endl;
        planner.startPlanning();
        
        // 打印状态信息
        std::cout << "\n📊 规划器状态:" << std::endl;
        planner.printStatus();
        
        std::cout << "\n✅ 局部规划器节点启动成功！" << std::endl;
        std::cout << "按 Ctrl+C 退出程序" << std::endl;
        
        // 主循环
        const double loop_rate = 100.0; // 100Hz
        const auto sleep_duration = std::chrono::duration<double>(1.0 / loop_rate);
        
        while (g_running && planner.isRunning()) {
            try {
                // 处理一次规划
                planner.processOnce();
                
                // 控制循环频率
                std::this_thread::sleep_for(sleep_duration);
            }
            catch (const std::exception& e) {
                std::cerr << "规划循环异常: " << e.what() << std::endl;
                break;
            }
        }
        
        // 停止规划器
        std::cout << "\n⏹️  停止局部规划器..." << std::endl;
        planner.stopPlanning();
        
        std::cout << "✅ 局部规划器节点已安全退出" << std::endl;
        
    }
    catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return -1;
    }
    catch (...) {
        std::cerr << "❌ 未知异常" << std::endl;
        return -1;
    }
    
    return 0;
}
