#include "local_planner.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <fstream>
#include <iomanip>
#include <ctime>

using namespace local_planner;

// 生成带时间戳的文件名
std::string generateTimestampedFilename() {
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << "trajectory_" << std::put_time(std::localtime(&time), "%Y%m%d_%H%M%S") << ".csv";
    return ss.str();
}

// 保存轨迹到CSV文件
void saveTrajectoryToCSV(const std::string& filename, const PathData& path) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filename << std::endl;
        return;
    }

    // 写入CSV头
    file << "timestamp,x,y,z,yaw,velocity" << std::endl;

    // 写入轨迹点
    for (const auto& point : path.poses) {
        file << std::fixed << std::setprecision(6)
             << path.header.stamp.toSec() << ","
             << point.position.x << ","
             << point.position.y << ","
             << point.position.z << ","
             << point.orientation.toYaw() << ","
             << "0.0" << std::endl;  // 速度暂时设为0.0
    }

    file.close();
    std::cout << "轨迹已保存到: " << filename << std::endl;
}

int main(int argc, char** argv) {
    std::cout << "=== 局部规划器使用示例 ===" << std::endl;
    
    try {
        // 解析命令行参数
        std::string config_path = "../config/local_planner.yaml";
        if (argc > 1) {
            config_path = argv[1];
        }
        std::cout << "使用配置文件: " << config_path << std::endl;
        
        // 创建局部规划器
        std::cout << "\n🚀 创建局部规划器..." << std::endl;
        LocalPlannerNoRos planner(config_path);
        
        // 设置回调函数
        std::cout << "\n📡 设置回调函数..." << std::endl;
        
        planner.setModePublishCallback([](const std::shared_ptr<const BoolMsg>& mode) {
            std::cout << "[回调] 调整模式: " << (mode->data ? "精调" : "粗调") << std::endl;
        });
        
        planner.setPathPublishCallback([](const std::shared_ptr<const PathData>& path) {
            std::cout << "[回调] 局部路径更新，点数: " << path->poses.size() << std::endl;
            if (!path->poses.empty()) {
                const auto& first_point = path->poses[0];
                std::cout << "  首点: (" << first_point.position.x << ", " 
                         << first_point.position.y << ", " << first_point.position.z << ")" << std::endl;
                
                // 保存轨迹到CSV文件
                std::string filename = generateTimestampedFilename();
                saveTrajectoryToCSV(filename, *path);
            }
        });
        
        planner.setStopPublishCallback([](const std::shared_ptr<const Int8Msg>& stop) {
            std::cout << "[回调] 停止信号: " << static_cast<int>(stop->data) << std::endl;
        });
        
        planner.setReplanPublishCallback([](const std::shared_ptr<const Int8Msg>& replan) {
            std::cout << "[回调] 重规划信号: " << static_cast<int>(replan->data) << std::endl;
        });
        
        // 初始化
        std::cout << "\n🔧 初始化局部规划器..." << std::endl;
        if (!planner.initialize()) {
            std::cerr << "❌ 初始化失败！" << std::endl;
            return -1;
        }
        
        // 启动规划器
        std::cout << "\n▶️  启动局部规划器..." << std::endl;
        planner.startPlanning();
        
        // 模拟数据输入
        std::cout << "\n📊 开始模拟数据输入..." << std::endl;
        
        // 模拟里程计数据
        std::cout << "\n1. 输入里程计数据..." << std::endl;
        OdometryData odom_data;
        odom_data.pose.position.x = 0.0;
        odom_data.pose.position.y = 0.0;
        odom_data.pose.position.z = 0.0;
        odom_data.pose.orientation = Quaternion::fromYaw(0.0);
        odom_data.header.setCurrentTime();
        planner.updateOdometry(odom_data);
        std::cout << "   里程计数据已输入: (0, 0, 0)" << std::endl;
        
        // 模拟目标点数据
        std::cout << "\n2. 输入目标点数据..." << std::endl;
        PoseStamped target;
        target.pose.position.x = 5.0;
        target.pose.position.y = 3.0;
        target.pose.position.z = 0.0;
        target.pose.orientation = Quaternion::fromYaw(0.5);
        target.header.setCurrentTime();
        planner.updateTarget(target);
        std::cout << "   目标点已设置: (5, 3, 0), yaw=0.5" << std::endl;
        
        // 模拟局部目标点
        std::cout << "\n3. 输入局部目标点..." << std::endl;
        PoseStamped goal;
        goal.pose.position.x = 2.0;
        goal.pose.position.y = 1.0;
        goal.pose.position.z = 0.0;
        goal.header.setCurrentTime();
        planner.updateGoal(goal);
        std::cout << "   局部目标点已设置: (2, 1, 0)" << std::endl;
        
        // 模拟点云数据
        std::cout << "\n4. 输入点云数据..." << std::endl;
        PointCloud2Data cloud_data;
        cloud_data.header.setCurrentTime();
        cloud_data.height = 1;
        cloud_data.width = 100;
        
        // 生成模拟点云数据 (简化)
        cloud_data.data.resize(100 * 16); // 100个点，每个点16字节
        for (int i = 0; i < 100; i++) {
            float x = static_cast<float>(i) * 0.1f;
            float y = 0.0f;
            float z = 0.0f;
            float intensity = 0.5f;
            
            size_t offset = i * 16;
            memcpy(&cloud_data.data[offset], &x, 4);
            memcpy(&cloud_data.data[offset + 4], &y, 4);
            memcpy(&cloud_data.data[offset + 8], &z, 4);
            memcpy(&cloud_data.data[offset + 12], &intensity, 4);
        }
        
        planner.updateTerrainCloud(cloud_data);
        std::cout << "   地形点云已输入: 100个点" << std::endl;
        
        // 运行一段时间
        std::cout << "\n⏱️  运行规划器 5 秒..." << std::endl;
        auto start_time = std::chrono::steady_clock::now();
        auto end_time = start_time + std::chrono::seconds(5);
        
        int iteration = 0;
        while (std::chrono::steady_clock::now() < end_time) {
            // 处理一次规划
            planner.processOnce();
            
            // 每秒更新一次里程计
            if (iteration % 100 == 0) {
                odom_data.pose.position.x += 0.1;
                odom_data.pose.position.y += 0.05;
                odom_data.header.setCurrentTime();
                planner.updateOdometry(odom_data);
                
                std::cout << "   更新里程计: (" << odom_data.pose.position.x 
                         << ", " << odom_data.pose.position.y << ")" << std::endl;
            }
            
            iteration++;
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        
        // 打印最终状态
        std::cout << "\n📊 最终状态:" << std::endl;
        planner.printStatus();
        
        // 测试参数设置
        std::cout << "\n🔧 测试参数设置..." << std::endl;
        planner.setMaxSpeed(1.2);
        planner.setVehicleDimensions(1.5, 1.0);
        planner.setPathScale(1.5);
        planner.setAdjacentRange(5.0);
        std::cout << "   参数设置完成" << std::endl;
        
        // 停止规划器
        std::cout << "\n⏹️  停止规划器..." << std::endl;
        planner.stopPlanning();
        
        std::cout << "\n✅ 示例程序运行完成！" << std::endl;
        
    }
    catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return -1;
    }
    catch (...) {
        std::cerr << "❌ 未知异常" << std::endl;
        return -1;
    }
    
    return 0;
}
