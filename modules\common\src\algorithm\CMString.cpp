﻿
#include "CMString.h"
#include <string.h>

// 字符串分割
cx_int StringSplit(std::vector<cx_string>& dst, const cx_string& src, const cx_string& separator)
{
    if (src.empty() || separator.empty())
        return 0;

    int nCount = 0;
    std::string temp;
    size_t pos = 0, offset = 0;

    // 分割第1~n-1个
    while ((pos = src.find_first_of(separator, offset)) != std::string::npos)
    {
        temp = src.substr(offset, pos - offset);
//        if (temp.length() > 0) {
//            dst.push_back(temp);
//            nCount++;
//        }
        dst.push_back(temp);
        nCount++;
        offset = pos + 1;
    }

    // 分割第n个
    temp = src.substr(offset, src.length() - offset);
    if (temp.length() > 0) {
        dst.push_back(temp);
        nCount++;
    }

    return nCount;
}

// 去掉前后空格
cx_string& StringTrim(cx_string &str)
{
    if (str.empty()) {
        return str;
    }

    str.erase(0, str.find_first_not_of(" "));
    str.erase(str.find_last_not_of(" ") + 1);

    return str;
}


string Vec2String(string split, vector<cx_int64>& idvt)
{
	if (idvt.size() == 0)
	{
		return "";
	}
	string str = to_string(idvt.at(0));
	for (int i = 1; i < idvt.size(); i++)
	{
		str += split;
		str += to_string(idvt.at(i));
	}
	return str;
}


#include <string>
#include <locale.h>
using namespace std;

cx_string ws2s(const cx_wstring& ws)
{
    cx_string curLocale = setlocale(LC_ALL, NULL); // curLocale = "C";
    setlocale(LC_ALL, "chs");
    const wchar_t* _Source = ws.c_str();
    size_t _Dsize = 2 * ws.size() + 1;
    char *_Dest = new char[_Dsize];
    memset(_Dest,0,_Dsize);
    wcstombs(_Dest,_Source,_Dsize);
    cx_string result = _Dest;
    delete []_Dest;
    setlocale(LC_ALL, curLocale.c_str());

    return result;
}

cx_wstring s2ws(const cx_string& s)
{
    cx_string curLocale = setlocale(LC_ALL, NULL); // curLocale = "C";
    setlocale(LC_ALL, "chs");
    const char* _Source = s.c_str();
    size_t _Dsize = s.size() + 1;
    wchar_t *_Dest = new wchar_t[_Dsize];
    wmemset(_Dest, 0, _Dsize);
    mbstowcs(_Dest,_Source,_Dsize);
    cx_wstring result = _Dest;
    delete []_Dest;
    setlocale(LC_ALL, curLocale.c_str());

    return result;
}
