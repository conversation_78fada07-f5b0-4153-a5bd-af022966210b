#pragma once
#if COMMUNICATION_TYPE == ROS1
#include <ros/ros.h>
#include <geometry_msgs/PoseStamped.h>
#include "subscriber_base.h"

namespace communication::ros1 {
class PoseDataSubscriberRos1 : public PoseDataSubscriberBase {
public:
    PoseDataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size = 10);

    ~PoseDataSubscriberRos1() = default;

    void PoseDataCallbackRos1(const geometry_msgs::PoseStamped::ConstPtr &pose_msg);

private:

    ros::NodeHandle &nh_;
    ros::Subscriber subscriber_;
};

} // namespace communication::ros1  
#endif // COMMUNICATION_TYPE == ROS1