#include "CMObserver.h"

void CMObservable::Attach(CMObserver* pObs)
{
    if (!pObs) return;
    m_setObs.insert(pObs);
}

void CMObservable::Detach(CMObserver* pObs) 
{
    if (!pObs) return;
    m_setObs.erase(pObs);
}

void CMObservable::DetachAll() 
{
    m_setObs.clear();
}

void CMObservable::SetChanged() 
{
    m_bChanged = true;
}

void CMObservable::ClearChanged()
{
    m_bChanged = false;
}

bool CMObservable::HasChanged() 
{
    return m_bChanged;
}

int CMObservable::GetObserversCount()
{
    return m_setObs.size();
}

void CMObservable::Notify(void* pArg /* = NULL */) 
{
    if (!HasChanged())
    {
        return;
    }

    //cout << "notify observers��" << endl;
    ClearChanged();
    
    for (set<CMObserver*>::iterator itr = m_setObs.begin(); itr != m_setObs.end(); itr++)
    {
        (*itr)->Update(this, pArg);
    }
}

SUBJECT_TYPE CMObservable::GetType()
{
    return m_eSubjectType;
}
