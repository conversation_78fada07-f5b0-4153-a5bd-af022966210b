#include "path_follower.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <iomanip>

using namespace path_follower;

int main(int argc, char** argv) {
    std::cout << "=== 路径跟随器使用示例 ===" << std::endl;
    
    try {
        // 解析命令行参数
        std::string config_path = "../config/path_follower.yaml";

        if (argc > 1) {
            config_path = argv[1];
        }
        std::cout << "使用配置文件: " << config_path << std::endl;
        
        // 创建路径跟随器
        std::cout << "\n🚀 创建路径跟随器..." << std::endl;
        PathFollowerNoRos follower(config_path);
        
        // 设置回调函数
        std::cout << "\n📡 设置回调函数..." << std::endl;
        
        // 速度命令回调
        follower.setSpeedPublishCallback([](const std::shared_ptr<TwistData>& cmd_vel) {
            std::cout << "[回调] 速度命令: v=" << std::fixed << std::setprecision(3) 
                     << cmd_vel->linear.x << " m/s, ω=" << cmd_vel->angular.z 
                     << " rad/s (" << cmd_vel->angular.z * 180.0 / PI << " deg/s)" << std::endl;
        });
        
        // 初始化
        std::cout << "\n🔧 初始化路径跟随器..." << std::endl;
        if (!follower.initialize()) {
            std::cerr << "❌ 初始化失败！" << std::endl;
            return -1;
        }
        
        // 启动跟随器
        std::cout << "\n▶️  启动路径跟随器..." << std::endl;
        follower.startFollowing();
        
        // 模拟数据输入
        std::cout << "\n📊 开始模拟数据输入..." << std::endl;
        
        // 1. 输入里程计数据
        std::cout << "\n1. 输入里程计数据..." << std::endl;
        OdometryData odom_data;
        odom_data.pose.position.x = 0.0;
        odom_data.pose.position.y = 0.0;
        odom_data.pose.position.z = 0.0;
        odom_data.pose.orientation = Quaternion::fromRPY(0.0, 0.0, 0.0);
        odom_data.header.setCurrentTime();
        follower.updateOdometry(odom_data);
        std::cout << "   里程计数据已输入: (0, 0, 0)" << std::endl;
        
        // 2. 设置目标点
        std::cout << "\n2. 设置目标点..." << std::endl;
        PoseStamped goal;
        goal.pose.position.x = 5.0;
        goal.pose.position.y = 3.0;
        goal.pose.position.z = 0.0;
        goal.pose.orientation = Quaternion::fromRPY(0.0, 0.0, 0.5);
        goal.header.setCurrentTime();
        follower.updateGoal(goal);
        std::cout << "   目标点已设置: (5, 3, 0), yaw=0.5 rad" << std::endl;
        
        // 3. 创建模拟路径
        std::cout << "\n3. 创建模拟路径..." << std::endl;
        PathData path_data;
        path_data.header.setCurrentTime();
        path_data.header.frame_id = "map";
        
        // 生成一条简单的直线路径
        int path_points = 20;
        path_data.resize(path_points);
        for (int i = 0; i < path_points; i++) {
            double t = static_cast<double>(i) / (path_points - 1);
            path_data.poses[i].pose.pose.position.x = t * 4.0;  // 0 到 4 米
            path_data.poses[i].pose.pose.position.y = t * 2.0;  // 0 到 2 米
            path_data.poses[i].pose.pose.position.z = 0.0;
            path_data.poses[i].pose.header.setCurrentTime();
        }
        
        follower.updatePath(path_data);
        std::cout << "   路径已输入: " << path_data.size() << " 个点" << std::endl;
        
        // 4. 设置调整模式
        std::cout << "\n4. 设置调整模式..." << std::endl;
        BoolMsg mode_msg(false);  // 粗调模式
        follower.updateMode(mode_msg);
        std::cout << "   调整模式已设置: " << (mode_msg.data ? "精调" : "粗调") << std::endl;
        
        // 5. 设置安全停止状态
        std::cout << "\n5. 设置安全停止状态..." << std::endl;
        Int8Msg stop_msg(0);  // 不停止
        follower.updateStop(stop_msg);
        std::cout << "   安全停止状态: " << static_cast<int>(stop_msg.data) << std::endl;
        
        // 运行一段时间
        std::cout << "\n⏱️  运行路径跟随器 10 秒..." << std::endl;
        auto start_time = std::chrono::steady_clock::now();
        auto end_time = start_time + std::chrono::seconds(10);
        
        int iteration = 0;
        int status_count = 0;
        while (std::chrono::steady_clock::now() < end_time) {
            // 处理一次路径跟随
            follower.processOnce();
            
            // 每秒更新一次里程计
            if (iteration % 100 == 0) {
                // 模拟车辆移动
                odom_data.pose.position.x += 0.05;  // 每秒移动5cm
                odom_data.pose.position.y += 0.03;  // 每秒移动3cm
                
                // 添加一些角度变化
                double yaw = 0.05 * sin(iteration * 0.01);
                odom_data.pose.orientation = Quaternion::fromRPY(0.0, 0.0, yaw);
                odom_data.header.setCurrentTime();
                follower.updateOdometry(odom_data);
                
                std::cout << "   更新里程计: (" << std::fixed << std::setprecision(3)
                         << odom_data.pose.position.x << ", " << odom_data.pose.position.y 
                         << "), yaw=" << yaw << " rad" << std::endl;
                
                // 每2秒显示一次状态
                if (status_count % 2 == 0) {
                    std::cout << "   距离目标: " << std::fixed << std::setprecision(3) 
                             << follower.getDistanceToGoal() << " m" << std::endl;
                    std::cout << "   当前速度: " << follower.getCurrentSpeed() << " m/s" << std::endl;
                    std::cout << "   当前角速度: " << follower.getCurrentYawRate() * 180.0 / PI << " deg/s" << std::endl;
                }
                status_count++;
            }
            
            iteration++;
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        
        // 打印最终状态
        std::cout << "\n📊 最终状态:" << std::endl;
        follower.printStatus();
        
        // 测试参数设置
        std::cout << "\n🔧 测试参数设置..." << std::endl;
        follower.setMaxSpeed(1.2);
        follower.setLookAheadDistance(0.8);
        follower.setTwoWayDrive(true);
        follower.setNoRotationAtGoal(false);
        follower.setYawRateGain(1.5);
        follower.setMaxYawRate(45.0);
        follower.setMaxAcceleration(4.0);
        std::cout << "   参数设置完成" << std::endl;
        
        // 测试安全停止
        std::cout << "\n🛑 测试安全停止..." << std::endl;
        Int8Msg emergency_stop(1);
        follower.updateStop(emergency_stop);
        std::cout << "   紧急停止已激活" << std::endl;
        
        // 运行几次看停止效果
        for (int i = 0; i < 5; i++) {
            follower.processOnce();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 恢复正常
        Int8Msg normal_operation(0);
        follower.updateStop(normal_operation);
        std::cout << "   恢复正常运行" << std::endl;
        
        // 测试暂停和恢复
        std::cout << "\n⏸️  测试暂停和恢复..." << std::endl;
        follower.pauseFollowing();
        std::cout << "   跟随器已暂停" << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        follower.resumeFollowing();
        std::cout << "   跟随器已恢复" << std::endl;
        
        // 测试Web目标设置
        std::cout << "\n🌐 测试Web目标设置..." << std::endl;
        PoseStamped web_goal;
        web_goal.pose.position.x = 8.0;
        web_goal.pose.position.y = 5.0;
        web_goal.pose.position.z = 0.0;
        web_goal.pose.orientation = Quaternion::fromRPY(0.0, 0.0, 1.0);
        web_goal.header.setCurrentTime();
        follower.updateWebGoal(web_goal);
        std::cout << "   Web目标已设置: (8, 5, 0), yaw=1.0 rad" << std::endl;
        
        // 停止跟随器
        std::cout << "\n⏹️  停止跟随器..." << std::endl;
        follower.stopFollowing();
        
        std::cout << "\n✅ 示例程序运行完成！" << std::endl;
        
        // 显示配置信息
        std::cout << "\n⚙️  当前配置信息:" << std::endl;
        auto config = follower.getConfig();
        std::cout << "   最大速度: " << config.maxSpeed << " m/s" << std::endl;
        std::cout << "   前视距离: " << config.lookAheadDis << " m" << std::endl;
        std::cout << "   角速度增益: " << config.yawRateGain << std::endl;
        std::cout << "   最大角速度: " << config.maxYawRate << " deg/s" << std::endl;
        std::cout << "   最大加速度: " << config.maxAccel << " m/s²" << std::endl;
        std::cout << "   双向驱动: " << (config.twoWayDrive ? "启用" : "禁用") << std::endl;
        std::cout << "   目标处禁止旋转: " << (config.noRotAtGoal ? "启用" : "禁用") << std::endl;
        
    }
    catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return -1;
    }
    catch (...) {
        std::cerr << "❌ 未知异常" << std::endl;
        return -1;
    }
    
    return 0;
}
